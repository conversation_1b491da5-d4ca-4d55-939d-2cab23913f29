globalThis.process??={},globalThis.process.env??={};import{g as getCollection}from"../chunks/_astro_content_3f_fRm09.mjs";import{b as generateExcerpt}from"../chunks/blog_C5qimf1U.mjs";export{renderers}from"../renderers.mjs";const prerender=!1,GET=async()=>{try{const e=(await getCollection("blog")).filter((e=>!e.data.draft)).sort(((e,t)=>new Date(t.data.publishDate).getTime()-new Date(e.data.publishDate).getTime())).slice(0,20),t="http://infpik.store",n=(new Date).toUTCString(),a=`<?xml version="1.0" encoding="UTF-8"?>\n<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom" xmlns:content="http://purl.org/rss/1.0/modules/content/">\n  <channel>\n    <title>InfPik Blog</title>\n    <description>Discover insights, tutorials, and inspiration about 3D illustration, design trends, and creative workflows.</description>\n    <link>${t}/blog</link>\n    <atom:link href="${t}/rss.xml" rel="self" type="application/rss+xml"/>\n    <language>en-us</language>\n    <lastBuildDate>${n}</lastBuildDate>\n    <managingEditor><EMAIL> (InfPik Team)</managingEditor>\n    <webMaster><EMAIL> (InfPik Team)</webMaster>\n    <generator>Astro</generator>\n    <image>\n      <url>${t}/logo.svg</url>\n      <title>InfPik Blog</title>\n      <link>${t}/blog</link>\n      <width>144</width>\n      <height>144</height>\n    </image>\n${e.map((e=>{const n=`${t}/blog/${e.slug}`,a=new Date(e.data.publishDate).toUTCString(),r=(generateExcerpt(e.body,200),e.body.replace(/```[\s\S]*?```/g,"").replace(/`[^`]*`/g,"").replace(/!\[.*?\]\(.*?\)/g,"").replace(/\[([^\]]*)\]\([^)]*\)/g,"$1").replace(/[#*_~`]/g,"").replace(/\s+/g," ").trim().substring(0,500)+"...");return`    <item>\n      <title><![CDATA[${e.data.title}]]></title>\n      <description><![CDATA[${e.data.description}]]></description>\n      <content:encoded><![CDATA[${r}]]></content:encoded>\n      <link>${n}</link>\n      <guid isPermaLink="true">${n}</guid>\n      <pubDate>${a}</pubDate>\n      ${e.data.author?`<author><EMAIL> (${e.data.author})</author>`:""}\n      ${e.data.category?`<category><![CDATA[${e.data.category}]]></category>`:""}\n      ${e.data.tags?e.data.tags.map((e=>`<category><![CDATA[${e}]]></category>`)).join("\n      "):""}\n      ${e.data.heroImage?`<enclosure url="${e.data.heroImage}" type="image/jpeg"/>`:""}\n    </item>`})).join("\n")}\n  </channel>\n</rss>`;return new Response(a,{headers:{"Content-Type":"application/rss+xml; charset=utf-8","Cache-Control":"public, max-age=3600"}})}catch(e){return console.error("Error generating RSS feed:",e),new Response("Error generating RSS feed",{status:500,headers:{"Content-Type":"text/plain"}})}},_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};