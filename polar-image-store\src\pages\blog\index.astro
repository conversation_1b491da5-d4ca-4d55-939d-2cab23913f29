---
import Layout from '../../layouts/Layout.astro';
import StructuredData from '../../components/StructuredData.astro';
import BlogCard from '../../components/blog/BlogCard.astro';
import BlogNavigation from '../../components/blog/BlogNavigation.astro';
import BlogSearch from '../../components/blog/BlogSearch.astro';
import { getFeaturedPosts, getRecentPosts } from '../../utils/blog';

// Get featured and recent posts
const featuredPosts = await getFeaturedPosts(3);
const recentPosts = await getRecentPosts(6);

const breadcrumbData = {
  itemListElement: [
    {
      "@type": "ListItem",
      position: 1,
      name: "Home",
      item: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}`
    },
    {
      "@type": "ListItem",
      position: 2,
      name: "Blog",
      item: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/blog`
    }
  ]
};
---

<Layout
  title="Blog - InfPik"
  description="Discover insights, tutorials, and inspiration about 3D illustration, design trends, and creative workflows."
  canonical={`${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/blog`}
  type="website"
>
  <!-- Breadcrumb Structured Data -->
  <StructuredData type="BreadcrumbList" data={breadcrumbData} />

  <div class="w-full px-4 md:px-8 py-8">
    <!-- Hero Section -->
    <section class="text-center mb-12">
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        Blog
      </h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
        Discover insights, tutorials, and inspiration about 3D illustration, design trends, and creative workflows.
      </p>

      <!-- Blog Search -->
      <div class="max-w-md mx-auto mb-8">
        <BlogSearch placeholder="Search blog posts..." variant="inline" />
      </div>
    </section>

    <!-- Blog Navigation -->
    <BlogNavigation variant="both" maxItems={6} />

    <!-- Featured Posts -->
    {featuredPosts.length > 0 && (
      <section class="mb-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Featured Posts</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredPosts.map((post) => (
            <BlogCard post={post} variant="featured" />
          ))}
        </div>
      </section>
    )}

    <!-- Recent Posts -->
    {recentPosts.length > 0 && (
      <section>
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Recent Posts</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {recentPosts.map((post) => (
            <BlogCard post={post} variant="default" />
          ))}
        </div>
      </section>
    )}

    <!-- Empty State -->
    {featuredPosts.length === 0 && recentPosts.length === 0 && (
      <section class="text-center py-16">
        <div class="max-w-md mx-auto">
          <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">No blog posts yet</h3>
          <p class="text-gray-600">Check back soon for new content!</p>
        </div>
      </section>
    )}

    <!-- RSS Feed Link -->
    <div class="mt-16 text-center border-t border-gray-200 pt-8">
      <div class="flex items-center justify-center gap-4">
        <a
          href="/rss.xml"
          class="inline-flex items-center gap-2 text-gray-600 hover:text-accent-600 transition-colors"
          target="_blank"
          rel="noopener noreferrer"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M6.503 20.752c0 1.794-1.456 3.248-3.251 3.248-1.796 0-3.252-1.454-3.252-3.248 0-1.794 1.456-3.248 3.252-3.248 1.795.001 3.251 1.454 3.251 3.248zm-6.503-12.572v4.811c6.05.062 10.96 4.966 11.022 11.009h4.817c-.062-8.71-7.118-15.758-15.839-15.82zm0-3.368c10.58.046 19.152 8.594 19.183 19.188h4.817c-.03-13.231-10.755-23.954-24-24v4.812z"/>
          </svg>
          Subscribe to RSS Feed
        </a>
      </div>
    </div>
  </div>
</Layout>
