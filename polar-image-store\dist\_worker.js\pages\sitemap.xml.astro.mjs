globalThis.process??={},globalThis.process.env??={};import{c as createPolarClient}from"../chunks/polar_D7XkB6p_.mjs";import{g as getCollection}from"../chunks/_astro_content_3f_fRm09.mjs";import{g as getBlogCategories,a as getBlogTags}from"../chunks/blog_C5qimf1U.mjs";export{renderers}from"../renderers.mjs";const GET=async({locals:e})=>{let t=[];try{const r=await getCollection("blog");r.filter((e=>!e.data.draft)).forEach((e=>{t.push({url:`https://infpik.store/blog/${e.slug}`,priority:"0.8",changefreq:"weekly",lastmod:e.data.updatedDate?.toISOString().split("T")[0]||e.data.publishDate.toISOString().split("T")[0]})}));(await getBlogCategories()).forEach((e=>{t.push({url:`https://infpik.store/blog/category/${e.slug}`,priority:"0.7",changefreq:"weekly"})}));(await getBlogTags()).forEach((e=>{t.push({url:`https://infpik.store/blog/tag/${e.slug}`,priority:"0.6",changefreq:"weekly"})}));const o=e?.runtime?.env,a=createPolarClient(o),s=o?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";if(s){const e=await a.products.list({organizationId:s,isArchived:!1}),r=e.result?.items||[],o=new Set,i=new Set;r.forEach((e=>{if(e.name){const r=e.name.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"");t.push({url:`https://infpik.store/products/${r}`,priority:"0.8",changefreq:"weekly"})}e.metadata&&Object.entries(e.metadata).forEach((([e,t])=>{e.startsWith("category:")&&"string"==typeof t&&o.add(t),e.startsWith("tag:")&&"string"==typeof t&&i.add(t)}))})),o.forEach((e=>{t.push({url:`https://infpik.store/products/category/${e}`,priority:"0.7",changefreq:"weekly"})})),i.forEach((e=>{t.push({url:`https://infpik.store/products/tag/${e}`,priority:"0.6",changefreq:"weekly"})}))}}catch(e){console.error("Error fetching dynamic pages for sitemap:",e)}const r=[{url:"https://infpik.store/",priority:"1.0",changefreq:"daily"},{url:"https://infpik.store/products/",priority:"0.9",changefreq:"daily"},{url:"https://infpik.store/blog/",priority:"0.9",changefreq:"daily"},{url:"https://infpik.store/trending/",priority:"0.8",changefreq:"daily"},{url:"https://infpik.store/about/",priority:"0.6",changefreq:"weekly"},{url:"https://infpik.store/privacy/",priority:"0.5",changefreq:"monthly"},{url:"https://infpik.store/terms/",priority:"0.5",changefreq:"monthly"},{url:"https://infpik.store/success/",priority:"0.3",changefreq:"yearly"},...t],o=(new Date).toISOString().split("T")[0],a=`<?xml version="1.0" encoding="UTF-8"?>\n<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"\n        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"\n        xmlns:xhtml="http://www.w3.org/1999/xhtml"\n        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"\n        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">\n${r.map((e=>`  <url>\n    <loc>${e.url}</loc>\n    <lastmod>${e.lastmod||o}</lastmod>\n    <changefreq>${e.changefreq}</changefreq>\n    <priority>${e.priority}</priority>\n  </url>`)).join("\n")}\n</urlset>`;return new Response(a,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"public, max-age=3600"}})},prerender=!1,_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};