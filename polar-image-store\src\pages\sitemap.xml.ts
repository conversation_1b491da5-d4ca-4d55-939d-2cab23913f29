import type { APIRoute } from 'astro';
import { createPolarClient } from '../utils/polar';
import { getCollection } from 'astro:content';
import { getBlogCategories, getBlogTags } from '../utils/blog';

export const GET: APIRoute = async ({ locals }) => {
  // Static pages
  const staticPages = [
    { url: 'https://infpik.store/', priority: '1.0', changefreq: 'daily' },
    { url: 'https://infpik.store/products/', priority: '0.9', changefreq: 'daily' },
    { url: 'https://infpik.store/blog/', priority: '0.9', changefreq: 'daily' },
    { url: 'https://infpik.store/trending/', priority: '0.8', changefreq: 'daily' },
    { url: 'https://infpik.store/about/', priority: '0.6', changefreq: 'weekly' },
    { url: 'https://infpik.store/privacy/', priority: '0.5', changefreq: 'monthly' },
    { url: 'https://infpik.store/terms/', priority: '0.5', changefreq: 'monthly' },
    { url: 'https://infpik.store/success/', priority: '0.3', changefreq: 'yearly' },
  ];

  // Dynamic pages (categories, tags, and blog posts)
  let dynamicPages: Array<{ url: string; priority: string; changefreq: string; lastmod?: string }> = [];

  try {
    // Add blog posts
    const blogPosts = await getCollection('blog');
    const publishedPosts = blogPosts.filter(post => !post.data.draft);

    publishedPosts.forEach(post => {
      dynamicPages.push({
        url: `https://infpik.store/blog/${post.slug}`,
        priority: '0.8',
        changefreq: 'weekly',
        lastmod: post.data.updatedDate?.toISOString().split('T')[0] || post.data.publishDate.toISOString().split('T')[0]
      });
    });

    // Add blog categories
    const blogCategories = await getBlogCategories();
    blogCategories.forEach(category => {
      dynamicPages.push({
        url: `https://infpik.store/blog/category/${category.slug}`,
        priority: '0.7',
        changefreq: 'weekly'
      });
    });

    // Add blog tags
    const blogTags = await getBlogTags();
    blogTags.forEach(tag => {
      dynamicPages.push({
        url: `https://infpik.store/blog/tag/${tag.slug}`,
        priority: '0.6',
        changefreq: 'weekly'
      });
    });
    // Get runtime environment from Cloudflare context
    const env = locals?.runtime?.env;
    const polar = createPolarClient(env);
    const organizationId = env?.POLAR_ORGANIZATION_ID || import.meta.env.POLAR_ORGANIZATION_ID;

    if (organizationId) {
      const response = await polar.products.list({
        organizationId,
        isArchived: false
      });

      const products = response.result?.items || [];
      const categories = new Set<string>();
      const tags = new Set<string>();

      // Extract categories, tags, and generate product URLs
      products.forEach(product => {
        // Add individual product URL
        if (product.name) {
          const slug = product.name.toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
          dynamicPages.push({
            url: `https://infpik.store/products/${slug}`,
            priority: '0.8',
            changefreq: 'weekly'
          });
        }

        // Extract categories and tags from metadata
        if (product.metadata) {
          Object.entries(product.metadata).forEach(([key, value]) => {
            if (key.startsWith('category:') && typeof value === 'string') {
              categories.add(value);
            }
            if (key.startsWith('tag:') && typeof value === 'string') {
              tags.add(value);
            }
          });
        }
      });

      // Add category pages
      categories.forEach(category => {
        dynamicPages.push({
          url: `https://infpik.store/products/category/${category}`,
          priority: '0.7',
          changefreq: 'weekly'
        });
      });

      // Add tag pages
      tags.forEach(tag => {
        dynamicPages.push({
          url: `https://infpik.store/products/tag/${tag}`,
          priority: '0.6',
          changefreq: 'weekly'
        });
      });
    }
  } catch (error) {
    console.error('Error fetching dynamic pages for sitemap:', error);
    // Continue with static pages only if dynamic fetch fails
  }

  // Combine all pages
  const allPages = [...staticPages, ...dynamicPages];

  // Get current date for lastmod
  const currentDate = new Date().toISOString().split('T')[0];

  // Generate XML with proper formatting
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">
${allPages.map(page => `  <url>
    <loc>${page.url}</loc>
    <lastmod>${(page as any).lastmod || currentDate}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

  return new Response(sitemap, {
    headers: {
      'Content-Type': 'application/xml; charset=utf-8',
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
    },
  });
};

export const prerender = false;
