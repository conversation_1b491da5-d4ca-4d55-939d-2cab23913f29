import type { APIRoute } from 'astro';
import { createPolarClient, transformPolarProduct, extractUniqueTags, getTagDisplayName } from '../../utils/polar';
import { searchPosts, getBlogTags } from '../../utils/blog';
import type { LocalProduct } from '../../types/polar';

export const prerender = false;

export const GET: APIRoute = async ({ url, locals }) => {
  try {
    const query = url.searchParams.get('q');

    // Get runtime environment from Cloudflare context
    const env = locals?.runtime?.env;
    const polar = createPolarClient(env);
    const organizationId = env?.POLAR_ORGANIZATION_ID || import.meta.env.POLAR_ORGANIZATION_ID;

    if (!organizationId) {
      return new Response(
        JSON.stringify({ error: 'Organization ID not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get products from Polar
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const products = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Extract all unique tags
    const allTags = extractUniqueTags(products);

    // If no query, return popular tags
    if (!query || query.trim().length < 2) {
      const popularProductTags = allTags
        .map(tag => {
          const tagProducts = products.filter(product =>
            product.tags && product.tags.includes(tag)
          );
          return {
            id: tag,
            name: getTagDisplayName(tag),
            displayName: getTagDisplayName(tag),
            count: tagProducts.length,
            url: `/products/tag/${encodeURIComponent(tag)}`,
            type: 'product'
          };
        })
        .sort((a, b) => b.count - a.count)
        .slice(0, 8);

      // Get popular blog tags
      const blogTags = await getBlogTags();
      const popularBlogTags = blogTags
        .slice(0, 4)
        .map(tag => ({
          id: tag.slug,
          name: tag.name,
          displayName: tag.name,
          count: tag.count,
          url: `/blog/tag/${tag.slug}`,
          type: 'blog'
        }));

      return new Response(
        JSON.stringify({
          results: [],
          popularTags: [...popularProductTags, ...popularBlogTags]
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
          }
        }
      );
    }

    // Search for both product tags and blog posts
    const searchTerm = query.toLowerCase().trim();

    // Filter product tags based on search query
    const matchingProductTags = allTags.filter(tag => {
      const tagName = getTagDisplayName(tag).toLowerCase();
      const tagId = tag.toLowerCase();

      // Match against both tag ID and display name
      return tagId.includes(searchTerm) || tagName.includes(searchTerm);
    });

    // Create product tag results with product counts
    const productTagResults = matchingProductTags.map(tag => {
      const tagProducts = products.filter(product =>
        product.tags && product.tags.includes(tag)
      );

      return {
        id: tag,
        name: getTagDisplayName(tag),
        displayName: getTagDisplayName(tag),
        count: tagProducts.length,
        url: `/products/tag/${encodeURIComponent(tag)}`,
        type: 'product'
      };
    });

    // Search blog posts
    const blogPosts = await searchPosts(searchTerm, 5);
    const blogResults = blogPosts.map(post => ({
      id: post.slug,
      name: post.data.title,
      displayName: post.data.title,
      description: post.data.description,
      url: `/blog/${post.slug}`,
      type: 'blog',
      category: post.data.category,
      publishDate: post.data.publishDate.toISOString(),
      heroImage: post.data.heroImage
    }));

    // Combine all results
    const allResults = [...productTagResults, ...blogResults];

    // Sort by relevance
    allResults.sort((a, b) => {
      const aExactMatch = a.name.toLowerCase() === searchTerm || a.id.toLowerCase() === searchTerm;
      const bExactMatch = b.name.toLowerCase() === searchTerm || b.id.toLowerCase() === searchTerm;

      if (aExactMatch && !bExactMatch) return -1;
      if (!aExactMatch && bExactMatch) return 1;

      // Prioritize blog posts for content searches
      if (a.type === 'blog' && b.type === 'product') return -1;
      if (a.type === 'product' && b.type === 'blog') return 1;

      // For product tags, sort by count
      if (a.type === 'product' && b.type === 'product') {
        if (b.count !== a.count) {
          return (b.count || 0) - (a.count || 0);
        }
      }

      // Finally, sort alphabetically
      return a.name.localeCompare(b.name);
    });

    return new Response(
      JSON.stringify({
        results: allResults,
        total: allResults.length,
        query: query
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60' // Cache for 1 minute
        }
      }
    );

  } catch (error) {
    console.error('Search API error:', error);
    return new Response(
      JSON.stringify({ error: 'Search failed' }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
};
