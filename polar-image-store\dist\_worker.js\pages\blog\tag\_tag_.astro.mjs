globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,d as renderComponent,r as renderTemplate,m as maybeRenderHead,b as addAttribute}from"../../../chunks/astro/server_B4z0x52z.mjs";import{$ as $$Layout}from"../../../chunks/Layout_Dwp3-WNN.mjs";import{g as getCollection}from"../../../chunks/_astro_content_3f_fRm09.mjs";import{$ as $$BlogCard}from"../../../chunks/BlogCard_BQ61Esuo.mjs";import{$ as $$StructuredData}from"../../../chunks/StructuredData_VefXz0ov.mjs";export{renderers}from"../../../renderers.mjs";const $$Astro=createAstro("https://infpik.store");async function getStaticPaths(){const t=(await getCollection("blog")).filter((t=>!t.data.draft)),e=t.flatMap((t=>t.data.tags||[]));return[...new Set(e)].map((e=>({params:{tag:e.toLowerCase().replace(/\s+/g,"-")},props:{tag:e,posts:t.filter((t=>t.data.tags?.includes(e))).sort(((t,e)=>new Date(e.data.publishDate).getTime()-new Date(t.data.publishDate).getTime()))}})))}const $$tag=createComponent((async(t,e,a)=>{const s=t.createAstro($$Astro,e,a);s.self=$$tag;const{tag:r,posts:o}=s.props,{tag:l}=s.params,n={itemListElement:[{"@type":"ListItem",position:1,name:"Home",item:"http://infpik.store"},{"@type":"ListItem",position:2,name:"Blog",item:"http://infpik.store/blog"},{"@type":"ListItem",position:3,name:`#${r}`,item:`http://infpik.store/blog/tag/${l}`}]};return renderTemplate`${renderComponent(t,"Layout",$$Layout,{title:`#${r} - InfPik Blog`,description:`Browse all blog posts tagged with ${r}. Discover related insights, tutorials, and inspiration.`,canonical:`http://infpik.store/blog/tag/${l}`,type:"website"},{default:async t=>renderTemplate`  ${renderComponent(t,"StructuredData",$$StructuredData,{type:"BreadcrumbList",data:n})} ${maybeRenderHead()}<div class="w-full px-4 md:px-8 py-8"> <!-- Breadcrumb Navigation --> <nav class="mb-8" aria-label="Breadcrumb"> <ol class="flex items-center space-x-2 text-sm text-gray-500"> <li><a href="/" class="hover:text-accent-600">Home</a></li> <li><span class="mx-2">/</span></li> <li><a href="/blog" class="hover:text-accent-600">Blog</a></li> <li><span class="mx-2">/</span></li> <li class="text-gray-900 font-medium">#${r}</li> </ol> </nav> <!-- Header --> <section class="text-center mb-12"> <div class="mb-4"> <span class="inline-block px-4 py-2 text-lg font-medium text-accent-700 bg-accent-100 rounded-full">
#${r} </span> </div> <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
Posts tagged with "${r}"
</h1> <p class="text-xl text-gray-600 max-w-2xl mx-auto"> ${o.length} post${1===o.length?"":"s"} with this tag
</p> </section> <!-- Posts Grid --> ${o.length>0?renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> ${o.map((e=>renderTemplate`${renderComponent(t,"BlogCard",$$BlogCard,{post:e,variant:"default"})}`))} </div>`:renderTemplate`<section class="text-center py-16"> <div class="max-w-md mx-auto"> <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path> </svg> <h3 class="text-xl font-semibold text-gray-900 mb-2">No posts found</h3> <p class="text-gray-600 mb-4">There are no posts with this tag yet.</p> <a href="/blog" class="inline-flex items-center px-4 py-2 bg-accent-600 text-white rounded-lg hover:bg-accent-700 transition-colors">
Browse all posts
</a> </div> </section>`} <!-- Related Tags --> ${o.length>0&&renderTemplate`<section class="mt-16 border-t border-gray-200 pt-12"> <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Tags</h2> <div class="flex flex-wrap gap-2"> ${[...new Set(o.flatMap((t=>t.data.tags||[])).filter((t=>t!==r)))].slice(0,10).map((t=>renderTemplate`<a${addAttribute(`/blog/tag/${t.toLowerCase().replace(/\s+/g,"-")}`,"href")} class="inline-block px-3 py-1 text-sm text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors">
#${t} </a>`))} </div> </section>`} <!-- Back to Blog --> <div class="mt-12 text-center"> <a href="/blog" class="inline-flex items-center text-accent-600 hover:text-accent-700 font-medium"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path> </svg>
Back to all posts
</a> </div> </div> `})}`}),"D:/code/image/polar-image-store/src/pages/blog/tag/[tag].astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/blog/tag/[tag].astro",$$url="/blog/tag/[tag]",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$tag,file:$$file,getStaticPaths:getStaticPaths,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};