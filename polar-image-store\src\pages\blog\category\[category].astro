---
import Layout from '../../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import BlogCard from '../../../components/blog/BlogCard.astro';
import StructuredData from '../../../components/StructuredData.astro';

export async function getStaticPaths() {
  const allPosts = await getCollection('blog');
  const publishedPosts = allPosts.filter(post => !post.data.draft);
  
  // Get all unique categories
  const categories = [...new Set(publishedPosts.map(post => post.data.category).filter(Boolean))];
  
  return categories.map((category) => ({
    params: { category: category.toLowerCase().replace(/\s+/g, '-') },
    props: { 
      category,
      posts: publishedPosts.filter(post => post.data.category === category)
        .sort((a, b) => new Date(b.data.publishDate).getTime() - new Date(a.data.publishDate).getTime())
    },
  }));
}

const { category, posts } = Astro.props;
const { category: categoryParam } = Astro.params;

const breadcrumbData = {
  itemListElement: [
    {
      "@type": "ListItem",
      position: 1,
      name: "Home",
      item: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}`
    },
    {
      "@type": "ListItem",
      position: 2,
      name: "Blog",
      item: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/blog`
    },
    {
      "@type": "ListItem",
      position: 3,
      name: category,
      item: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/blog/category/${categoryParam}`
    }
  ]
};
---

<Layout
  title={`${category} - InfPik Blog`}
  description={`Browse all blog posts in the ${category} category. Discover insights, tutorials, and inspiration.`}
  canonical={`${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/blog/category/${categoryParam}`}
  type="website"
>
  <!-- Breadcrumb Structured Data -->
  <StructuredData type="BreadcrumbList" data={breadcrumbData} />

  <div class="w-full px-4 md:px-8 py-8">
    <!-- Breadcrumb Navigation -->
    <nav class="mb-8" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-2 text-sm text-gray-500">
        <li><a href="/" class="hover:text-accent-600">Home</a></li>
        <li><span class="mx-2">/</span></li>
        <li><a href="/blog" class="hover:text-accent-600">Blog</a></li>
        <li><span class="mx-2">/</span></li>
        <li class="text-gray-900 font-medium">{category}</li>
      </ol>
    </nav>

    <!-- Header -->
    <section class="text-center mb-12">
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        {category}
      </h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        {posts.length} post{posts.length === 1 ? '' : 's'} in this category
      </p>
    </section>

    <!-- Posts Grid -->
    {posts.length > 0 ? (
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {posts.map((post) => (
          <BlogCard post={post} variant="default" />
        ))}
      </div>
    ) : (
      <section class="text-center py-16">
        <div class="max-w-md mx-auto">
          <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">No posts found</h3>
          <p class="text-gray-600 mb-4">There are no posts in this category yet.</p>
          <a
            href="/blog"
            class="inline-flex items-center px-4 py-2 bg-accent-600 text-white rounded-lg hover:bg-accent-700 transition-colors"
          >
            Browse all posts
          </a>
        </div>
      </section>
    )}

    <!-- Back to Blog -->
    <div class="mt-12 text-center">
      <a
        href="/blog"
        class="inline-flex items-center text-accent-600 hover:text-accent-700 font-medium"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
        Back to all posts
      </a>
    </div>
  </div>
</Layout>
