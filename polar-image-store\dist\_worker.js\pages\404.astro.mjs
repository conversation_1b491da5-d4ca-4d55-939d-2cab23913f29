globalThis.process??={},globalThis.process.env??={};import{a as createComponent,d as renderComponent,e as renderScript,r as renderTemplate,m as maybeRenderHead}from"../chunks/astro/server_B4z0x52z.mjs";import{$ as $$Layout}from"../chunks/Layout_Dwp3-WNN.mjs";import{$ as $$Image}from"../chunks/_astro_assets_dhiuug9q.mjs";export{renderers}from"../renderers.mjs";const $$404=createComponent((async(e,t,r)=>renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:"Page Not Found - InfPik",description:"The page you're looking for doesn't exist. Browse our beautiful collection of digital images instead.",noindex:!0,"data-astro-cid-zetdm5md":!0},{default:async e=>renderTemplate` ${maybeRenderHead()}<div class="min-h-[60vh] flex items-center justify-center px-4" data-astro-cid-zetdm5md> <div class="text-center max-w-2xl mx-auto" data-astro-cid-zetdm5md> <!-- 404 Illustration --> <div class="mb-8" data-astro-cid-zetdm5md> <div class="flex items-center justify-center gap-2" data-astro-cid-zetdm5md> <div class="text-8xl md:text-9xl font-bold text-primary-100 select-none" data-astro-cid-zetdm5md>
4
</div> <div class="flex items-center justify-center" data-astro-cid-zetdm5md> ${renderComponent(e,"Image",$$Image,{src:"/logo.svg",alt:"InfPik Logo",width:64,height:64,class:"w-16 h-16 text-accent-600 opacity-80","data-astro-cid-zetdm5md":!0})} </div> <div class="text-8xl md:text-9xl font-bold text-primary-100 select-none" data-astro-cid-zetdm5md>
4
</div> </div> </div> <!-- Error Message --> <div class="mb-8" data-astro-cid-zetdm5md> <h1 class="text-3xl md:text-4xl font-bold text-primary-900 mb-4" data-astro-cid-zetdm5md>
Oops! Page Not Found
</h1> <p class="text-lg text-primary-600 mb-6" data-astro-cid-zetdm5md>
The page you're looking for seems to have wandered off into the digital void. 
          Don't worry though – we have plenty of beautiful images waiting for you!
</p> </div> <!-- Action Buttons --> <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8" data-astro-cid-zetdm5md> <a href="/" class="btn-primary" data-astro-cid-zetdm5md> <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-zetdm5md> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" data-astro-cid-zetdm5md></path> </svg>
Go Home
</a> <a href="/products" class="btn-secondary" data-astro-cid-zetdm5md> <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-zetdm5md> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" data-astro-cid-zetdm5md></path> </svg>
Browse Images
</a> <a href="/trending" class="btn-secondary" data-astro-cid-zetdm5md> <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-zetdm5md> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" data-astro-cid-zetdm5md></path> </svg>
Trending Now
</a> </div> <!-- Search Suggestion --> <div class="bg-primary-50 rounded-2xl p-6 border border-primary-100" data-astro-cid-zetdm5md> <h3 class="text-lg font-semibold text-primary-900 mb-3" data-astro-cid-zetdm5md>
Looking for something specific?
</h3> <p class="text-primary-600 mb-4" data-astro-cid-zetdm5md>
Try searching for images by category, style, or keyword.
</p> <div class="relative max-w-md mx-auto" data-astro-cid-zetdm5md> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" data-astro-cid-zetdm5md> <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-zetdm5md> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" data-astro-cid-zetdm5md></path> </svg> </div> <input type="text" id="error-page-search" class="block w-full pl-10 pr-4 py-3 border border-primary-200 rounded-full bg-white text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200" placeholder="Search for images..." autocomplete="off" data-astro-cid-zetdm5md> <!-- Search results dropdown (hidden by default) --> <div id="errorPageSearchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto" data-astro-cid-zetdm5md> <!-- Search results will be populated here --> </div> </div> </div> <!-- Popular Categories --> <div class="mt-8" data-astro-cid-zetdm5md> <h3 class="text-lg font-semibold text-primary-900 mb-4" data-astro-cid-zetdm5md>
Popular Categories
</h3> <div class="flex flex-wrap gap-2 justify-center" data-astro-cid-zetdm5md> <a href="/products/category/nature" class="inline-flex items-center px-4 py-2 bg-white border border-primary-200 rounded-full text-sm font-medium text-primary-700 hover:bg-primary-50 hover:border-accent-300 transition-all duration-200" data-astro-cid-zetdm5md>
🌿 Nature
</a> <a href="/products/category/abstract" class="inline-flex items-center px-4 py-2 bg-white border border-primary-200 rounded-full text-sm font-medium text-primary-700 hover:bg-primary-50 hover:border-accent-300 transition-all duration-200" data-astro-cid-zetdm5md>
🎨 Abstract
</a> <a href="/products/category/technology" class="inline-flex items-center px-4 py-2 bg-white border border-primary-200 rounded-full text-sm font-medium text-primary-700 hover:bg-primary-50 hover:border-accent-300 transition-all duration-200" data-astro-cid-zetdm5md>
💻 Technology
</a> <a href="/products/category/business" class="inline-flex items-center px-4 py-2 bg-white border border-primary-200 rounded-full text-sm font-medium text-primary-700 hover:bg-primary-50 hover:border-accent-300 transition-all duration-200" data-astro-cid-zetdm5md>
💼 Business
</a> <a href="/products/category/lifestyle" class="inline-flex items-center px-4 py-2 bg-white border border-primary-200 rounded-full text-sm font-medium text-primary-700 hover:bg-primary-50 hover:border-accent-300 transition-all duration-200" data-astro-cid-zetdm5md>
✨ Lifestyle
</a> </div> </div> </div> </div> `})} ${renderScript(e,"D:/code/image/polar-image-store/src/pages/404.astro?astro&type=script&index=0&lang.ts")} `),"D:/code/image/polar-image-store/src/pages/404.astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/404.astro",$$url="/404",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$404,file:$$file,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};