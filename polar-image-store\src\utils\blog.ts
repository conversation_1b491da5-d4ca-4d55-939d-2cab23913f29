import { getCollection, type CollectionEntry } from 'astro:content';

export type BlogPost = CollectionEntry<'blog'>;

/**
 * Calculate reading time for a blog post
 * @param content - The markdown content of the post
 * @param wordsPerMinute - Average reading speed (default: 200 words per minute)
 * @returns Reading time in minutes
 */
export function calculateReadingTime(content: string, wordsPerMinute: number = 200): number {
  // Remove markdown syntax and count words
  const cleanContent = content
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/`[^`]*`/g, '') // Remove inline code
    .replace(/!\[.*?\]\(.*?\)/g, '') // Remove images
    .replace(/\[.*?\]\(.*?\)/g, '') // Remove links
    .replace(/[#*_~`]/g, '') // Remove markdown formatting
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();

  const wordCount = cleanContent.split(/\s+/).filter(word => word.length > 0).length;
  return Math.ceil(wordCount / wordsPerMinute);
}

/**
 * Generate an excerpt from blog post content
 * @param content - The markdown content of the post
 * @param maxLength - Maximum length of the excerpt (default: 160 characters)
 * @returns Excerpt string
 */
export function generateExcerpt(content: string, maxLength: number = 160): string {
  // Remove markdown syntax and get plain text
  const cleanContent = content
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/`[^`]*`/g, '') // Remove inline code
    .replace(/!\[.*?\]\(.*?\)/g, '') // Remove images
    .replace(/\[([^\]]*)\]\([^)]*\)/g, '$1') // Convert links to text
    .replace(/[#*_~`]/g, '') // Remove markdown formatting
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();

  if (cleanContent.length <= maxLength) {
    return cleanContent;
  }

  // Find the last complete word within the limit
  const truncated = cleanContent.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');
  
  if (lastSpaceIndex > 0) {
    return truncated.substring(0, lastSpaceIndex) + '...';
  }
  
  return truncated + '...';
}

/**
 * Get all published blog posts sorted by publish date
 * @returns Array of published blog posts
 */
export async function getPublishedPosts(): Promise<BlogPost[]> {
  const allPosts = await getCollection('blog');
  return allPosts
    .filter(post => !post.data.draft)
    .sort((a, b) => new Date(b.data.publishDate).getTime() - new Date(a.data.publishDate).getTime());
}

/**
 * Get featured blog posts
 * @param limit - Maximum number of featured posts to return (default: 3)
 * @returns Array of featured blog posts
 */
export async function getFeaturedPosts(limit: number = 3): Promise<BlogPost[]> {
  const publishedPosts = await getPublishedPosts();
  return publishedPosts
    .filter(post => post.data.featured)
    .slice(0, limit);
}

/**
 * Get recent blog posts (excluding featured posts)
 * @param limit - Maximum number of recent posts to return (default: 6)
 * @returns Array of recent blog posts
 */
export async function getRecentPosts(limit: number = 6): Promise<BlogPost[]> {
  const publishedPosts = await getPublishedPosts();
  return publishedPosts
    .filter(post => !post.data.featured)
    .slice(0, limit);
}

/**
 * Get all unique categories from blog posts
 * @returns Array of category objects with name and count
 */
export async function getBlogCategories(): Promise<Array<{name: string, count: number, slug: string}>> {
  const publishedPosts = await getPublishedPosts();
  const categoryMap = new Map<string, number>();

  publishedPosts.forEach(post => {
    if (post.data.category) {
      const count = categoryMap.get(post.data.category) || 0;
      categoryMap.set(post.data.category, count + 1);
    }
  });

  return Array.from(categoryMap.entries())
    .map(([name, count]) => ({
      name,
      count,
      slug: name.toLowerCase().replace(/\s+/g, '-')
    }))
    .sort((a, b) => b.count - a.count);
}

/**
 * Get all unique tags from blog posts
 * @returns Array of tag objects with name and count
 */
export async function getBlogTags(): Promise<Array<{name: string, count: number, slug: string}>> {
  const publishedPosts = await getPublishedPosts();
  const tagMap = new Map<string, number>();

  publishedPosts.forEach(post => {
    if (post.data.tags) {
      post.data.tags.forEach(tag => {
        const count = tagMap.get(tag) || 0;
        tagMap.set(tag, count + 1);
      });
    }
  });

  return Array.from(tagMap.entries())
    .map(([name, count]) => ({
      name,
      count,
      slug: name.toLowerCase().replace(/\s+/g, '-')
    }))
    .sort((a, b) => b.count - a.count);
}

/**
 * Get related posts based on category and tags
 * @param currentPost - The current blog post
 * @param limit - Maximum number of related posts to return (default: 3)
 * @returns Array of related blog posts
 */
export async function getRelatedPosts(currentPost: BlogPost, limit: number = 3): Promise<BlogPost[]> {
  const allPosts = await getPublishedPosts();
  
  // Filter out the current post
  const otherPosts = allPosts.filter(post => post.slug !== currentPost.slug);
  
  // Calculate similarity scores
  const postsWithScores = otherPosts.map(post => {
    let score = 0;
    
    // Same category gets higher score
    if (post.data.category === currentPost.data.category) {
      score += 3;
    }
    
    // Shared tags get points
    if (currentPost.data.tags && post.data.tags) {
      const sharedTags = currentPost.data.tags.filter(tag => 
        post.data.tags?.includes(tag)
      );
      score += sharedTags.length * 2;
    }
    
    return { post, score };
  });
  
  // Sort by score and return top results
  return postsWithScores
    .filter(item => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.post);
}

/**
 * Get posts by category
 * @param category - Category name
 * @returns Array of blog posts in the specified category
 */
export async function getPostsByCategory(category: string): Promise<BlogPost[]> {
  const publishedPosts = await getPublishedPosts();
  return publishedPosts.filter(post => 
    post.data.category?.toLowerCase().replace(/\s+/g, '-') === category.toLowerCase()
  );
}

/**
 * Get posts by tag
 * @param tag - Tag name
 * @returns Array of blog posts with the specified tag
 */
export async function getPostsByTag(tag: string): Promise<BlogPost[]> {
  const publishedPosts = await getPublishedPosts();
  return publishedPosts.filter(post => 
    post.data.tags?.some(postTag => 
      postTag.toLowerCase().replace(/\s+/g, '-') === tag.toLowerCase()
    )
  );
}

/**
 * Search blog posts by title, description, and content
 * @param query - Search query
 * @param limit - Maximum number of results to return (default: 10)
 * @returns Array of matching blog posts
 */
export async function searchPosts(query: string, limit: number = 10): Promise<BlogPost[]> {
  if (!query.trim()) {
    return [];
  }

  const publishedPosts = await getPublishedPosts();
  const searchTerm = query.toLowerCase();
  
  const matchingPosts = publishedPosts.filter(post => {
    const title = post.data.title.toLowerCase();
    const description = post.data.description.toLowerCase();
    const content = post.body.toLowerCase();
    const category = post.data.category?.toLowerCase() || '';
    const tags = post.data.tags?.join(' ').toLowerCase() || '';
    
    return title.includes(searchTerm) ||
           description.includes(searchTerm) ||
           content.includes(searchTerm) ||
           category.includes(searchTerm) ||
           tags.includes(searchTerm);
  });
  
  return matchingPosts.slice(0, limit);
}

/**
 * Format date for display
 * @param date - Date object
 * @param format - Format type ('full', 'short', 'medium')
 * @returns Formatted date string
 */
export function formatDate(date: Date, format: 'full' | 'short' | 'medium' = 'medium'): string {
  const options: Intl.DateTimeFormatOptions = {
    full: { year: 'numeric', month: 'long', day: 'numeric' },
    medium: { year: 'numeric', month: 'short', day: 'numeric' },
    short: { month: 'short', day: 'numeric' }
  };
  
  return date.toLocaleDateString('en-US', options[format]);
}
