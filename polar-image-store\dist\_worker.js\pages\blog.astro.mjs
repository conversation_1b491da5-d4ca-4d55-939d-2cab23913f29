globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,m as maybeRenderHead,b as addAttribute,r as renderTemplate,e as renderScript,d as renderComponent}from"../chunks/astro/server_B4z0x52z.mjs";import{$ as $$Layout}from"../chunks/Layout_Dwp3-WNN.mjs";import{$ as $$StructuredData}from"../chunks/StructuredData_VefXz0ov.mjs";import{$ as $$BlogCard}from"../chunks/BlogCard_BQ61Esuo.mjs";import{g as getBlogCategories,a as getBlogTags,c as getFeaturedPosts,d as getRecentPosts}from"../chunks/blog_C5qimf1U.mjs";export{renderers}from"../renderers.mjs";const $$Astro$1=createAstro("https://infpik.store"),$$BlogNavigation=createComponent((async(e,t,a)=>{const r=e.createAstro($$Astro$1,t,a);r.self=$$BlogNavigation;const{variant:o="both",activeCategory:s,activeTag:i,maxItems:l=8}=r.props;let n=[],d=[];try{"categories"!==o&&"both"!==o||(n=await getBlogCategories()),"tags"!==o&&"both"!==o||(d=await getBlogTags())}catch(e){console.error("Error fetching blog navigation data:",e)}return renderTemplate`${maybeRenderHead()}<div class="blog-navigation" data-astro-cid-cp4qy4l4> ${("categories"===o||"both"===o)&&n.length>0&&renderTemplate`<section class="mb-8" data-astro-cid-cp4qy4l4> <h3 class="text-lg font-semibold text-gray-900 mb-4" data-astro-cid-cp4qy4l4>Categories</h3> <div class="overflow-x-auto scrollbar-hide" data-astro-cid-cp4qy4l4> <div class="flex gap-2 pb-2 min-w-max" data-astro-cid-cp4qy4l4> <!-- All Categories Link --> <a href="/blog"${addAttribute("category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap border "+(s?"bg-white text-primary-900 border-primary-200 shadow-sm hover:bg-primary-50 hover:border-accent-500":"bg-accent-500 text-white border-accent-500 shadow-md"),"class")} data-astro-cid-cp4qy4l4>
All Posts
</a> ${n.slice(0,l).map((e=>renderTemplate`<a${addAttribute(`/blog/category/${e.slug}`,"href")}${addAttribute("category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap border "+(s===e.slug?"bg-accent-500 text-white border-accent-500 shadow-md":"bg-white text-primary-900 border-primary-200 shadow-sm hover:bg-primary-50 hover:border-accent-500"),"class")} data-astro-cid-cp4qy4l4> ${e.name} <span class="text-xs opacity-75" data-astro-cid-cp4qy4l4>(${e.count})</span> </a>`))} </div> </div> </section>`} ${("tags"===o||"both"===o)&&d.length>0&&renderTemplate`<section class="mb-8" data-astro-cid-cp4qy4l4> <h3 class="text-lg font-semibold text-gray-900 mb-4" data-astro-cid-cp4qy4l4>Popular Tags</h3> <div class="overflow-x-auto scrollbar-hide" data-astro-cid-cp4qy4l4> <div class="flex gap-2 pb-2 min-w-max" data-astro-cid-cp4qy4l4> ${d.slice(0,l).map((e=>renderTemplate`<a${addAttribute(`/blog/tag/${e.slug}`,"href")}${addAttribute("tag-tab flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium transition-all whitespace-nowrap "+(i===e.slug?"bg-accent-100 text-accent-700 border border-accent-300":"bg-gray-100 text-gray-700 hover:bg-gray-200"),"class")} data-astro-cid-cp4qy4l4>
#${e.name} <span class="text-xs opacity-75" data-astro-cid-cp4qy4l4>(${e.count})</span> </a>`))} </div> </div> </section>`} </div> `}),"D:/code/image/polar-image-store/src/components/blog/BlogNavigation.astro",void 0),$$Astro=createAstro("https://infpik.store"),$$BlogSearch=createComponent((async(e,t,a)=>{const r=e.createAstro($$Astro,t,a);r.self=$$BlogSearch;const{placeholder:o="Search blog posts...",variant:s="inline"}=r.props;return renderTemplate`${"inline"===s?renderTemplate`<!-- Inline Blog Search -->
  ${maybeRenderHead()}<div class="blog-search-container" data-astro-cid-b76riouj><div class="relative" data-astro-cid-b76riouj><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" data-astro-cid-b76riouj><svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-b76riouj><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" data-astro-cid-b76riouj></path></svg></div><input type="text" id="blogSearchInput"${addAttribute(o,"placeholder")} class="block w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200" data-astro-cid-b76riouj></div><!-- Search Results Container --><div id="blogSearchResults" class="hidden absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto" data-astro-cid-b76riouj><!-- Results will be populated by JavaScript --></div></div>`:renderTemplate`<!-- Modal Integration - Add blog search tab to existing SearchModal -->
  <div class="blog-search-modal-integration" data-astro-cid-b76riouj><!-- This will be integrated into the existing SearchModal --></div>`}${renderScript(e,"D:/code/image/polar-image-store/src/components/blog/BlogSearch.astro?astro&type=script&index=0&lang.ts")}`}),"D:/code/image/polar-image-store/src/components/blog/BlogSearch.astro",void 0),$$Index=createComponent((async(e,t,a)=>{const r=await getFeaturedPosts(3),o=await getRecentPosts(6),s={itemListElement:[{"@type":"ListItem",position:1,name:"Home",item:"http://infpik.store"},{"@type":"ListItem",position:2,name:"Blog",item:"http://infpik.store/blog"}]};return renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:"Blog - InfPik",description:"Discover insights, tutorials, and inspiration about 3D illustration, design trends, and creative workflows.",canonical:"http://infpik.store/blog",type:"website"},{default:async e=>renderTemplate`  ${renderComponent(e,"StructuredData",$$StructuredData,{type:"BreadcrumbList",data:s})} ${maybeRenderHead()}<div class="w-full px-4 md:px-8 py-8"> <!-- Hero Section --> <section class="text-center mb-12"> <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
Blog
</h1> <p class="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
Discover insights, tutorials, and inspiration about 3D illustration, design trends, and creative workflows.
</p> <!-- Blog Search --> <div class="max-w-md mx-auto mb-8"> ${renderComponent(e,"BlogSearch",$$BlogSearch,{placeholder:"Search blog posts...",variant:"inline"})} </div> </section> <!-- Blog Navigation --> ${renderComponent(e,"BlogNavigation",$$BlogNavigation,{variant:"both",maxItems:6})} <!-- Featured Posts --> ${r.length>0&&renderTemplate`<section class="mb-16"> <h2 class="text-2xl font-bold text-gray-900 mb-8">Featured Posts</h2> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> ${r.map((t=>renderTemplate`${renderComponent(e,"BlogCard",$$BlogCard,{post:t,variant:"featured"})}`))} </div> </section>`} <!-- Recent Posts --> ${o.length>0&&renderTemplate`<section> <h2 class="text-2xl font-bold text-gray-900 mb-8">Recent Posts</h2> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> ${o.map((t=>renderTemplate`${renderComponent(e,"BlogCard",$$BlogCard,{post:t,variant:"default"})}`))} </div> </section>`} <!-- Empty State --> ${0===r.length&&0===o.length&&renderTemplate`<section class="text-center py-16"> <div class="max-w-md mx-auto"> <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path> </svg> <h3 class="text-xl font-semibold text-gray-900 mb-2">No blog posts yet</h3> <p class="text-gray-600">Check back soon for new content!</p> </div> </section>`} <!-- RSS Feed Link --> <div class="mt-16 text-center border-t border-gray-200 pt-8"> <div class="flex items-center justify-center gap-4"> <a href="/rss.xml" class="inline-flex items-center gap-2 text-gray-600 hover:text-accent-600 transition-colors" target="_blank" rel="noopener noreferrer"> <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"> <path d="M6.503 20.752c0 1.794-1.456 3.248-3.251 3.248-1.796 0-3.252-1.454-3.252-3.248 0-1.794 1.456-3.248 3.252-3.248 1.795.001 3.251 1.454 3.251 3.248zm-6.503-12.572v4.811c6.05.062 10.96 4.966 11.022 11.009h4.817c-.062-8.71-7.118-15.758-15.839-15.82zm0-3.368c10.58.046 19.152 8.594 19.183 19.188h4.817c-.03-13.231-10.755-23.954-24-24v4.812z"></path> </svg>
Subscribe to RSS Feed
</a> </div> </div> </div> `})}`}),"D:/code/image/polar-image-store/src/pages/blog/index.astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/blog/index.astro",$$url="/blog",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$Index,file:$$file,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};