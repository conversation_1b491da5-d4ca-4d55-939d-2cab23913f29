globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,d as renderComponent,r as renderTemplate,m as maybeRenderHead,b as addAttribute,s as spreadAttributes}from"./astro/server_B4z0x52z.mjs";import{$ as $$Image}from"./_astro_assets_dhiuug9q.mjs";import{getResponsiveImageUrls,getOptimizedImageUrl,generateSizesAttribute}from"./imageOptimization_DKmSV198.mjs";const $$Astro=createAstro("https://infpik.store"),$$OptimizedImage=createComponent((async(e,t,s)=>{const i=e.createAstro($$Astro,t,s);i.self=$$OptimizedImage;const{src:r,alt:a,width:o=800,height:d=600,quality:m=85,format:n="auto",fit:l="scale-down",loading:p="lazy",fetchpriority:g="auto",class:c="",style:$="",responsive:u=!0,sizes:h=[320,640,960,1280,1920],densities:b=[1,2],preset:A,...f}=i.props;let z,I;if(A){const{ImagePresets:e}=await import("./imageOptimization_DKmSV198.mjs");z=e[A](r)}else if(u){const e=getResponsiveImageUrls(r,{sizes:h,densities:b,width:o,height:d,quality:m,format:n,fit:l});z=e.src,I=e.srcset}else{z=getOptimizedImageUrl(r,{width:o,height:d,quality:m,format:n,fit:l})}const y=u?generateSizesAttribute():void 0,w=r.startsWith("/")||r.includes("placeholder");return renderTemplate`${w?renderTemplate`<!-- Use Astro's built-in Image component for local images -->
  ${renderComponent(e,"Image",$$Image,{src:r,alt:a,width:o,height:d,loading:p,fetchpriority:g,class:c,style:$,...f})}`:renderTemplate`<!-- Use optimized external image with Cloudflare Image Transform -->
  ${maybeRenderHead()}<img${addAttribute(z,"src")}${addAttribute(I,"srcset")}${addAttribute(y,"sizes")}${addAttribute(a,"alt")}${addAttribute(o,"width")}${addAttribute(d,"height")}${addAttribute(p,"loading")}${addAttribute(g,"fetchpriority")}${addAttribute(c,"class")}${addAttribute($,"style")}${spreadAttributes(f)}>`}`}),"D:/code/image/polar-image-store/src/components/OptimizedImage.astro",void 0);export{$$OptimizedImage as $};