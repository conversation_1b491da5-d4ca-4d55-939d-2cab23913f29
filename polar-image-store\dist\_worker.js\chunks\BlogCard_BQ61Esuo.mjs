globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,m as maybeRenderHead,b as addAttribute,r as renderTemplate,d as renderComponent,F as Fragment}from"./astro/server_B4z0x52z.mjs";const $$Astro=createAstro("https://infpik.store"),$$BlogCard=createComponent(((e,t,a)=>{const r=e.createAstro($$Astro,t,a);r.self=$$BlogCard;const{post:o,variant:d="default"}=r.props,s=o.body.split(/\s+/).length,n=Math.ceil(s/200);return renderTemplate`${maybeRenderHead()}<article${addAttribute({default:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow",featured:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow",compact:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"}[d],"class")}> ${o.data.heroImage&&renderTemplate`<img${addAttribute(o.data.heroImage,"src")}${addAttribute(o.data.title,"alt")}${addAttribute({default:"w-full h-40 object-cover",featured:"w-full h-48 object-cover",compact:"w-full h-32 object-cover"}[d],"class")} loading="lazy">`} <div${addAttribute({default:"p-4",featured:"p-6",compact:"p-3"}[d],"class")}> <!-- Meta Information --> <div class="flex items-center gap-2 text-sm text-gray-500 mb-2"> <time${addAttribute(o.data.publishDate.toISOString(),"datetime")}> ${o.data.publishDate.toLocaleDateString("en-US",{year:"compact"===d?"2-digit":"numeric",month:"compact"===d?"short":"long",day:"numeric"})} </time> ${o.data.category&&renderTemplate`${renderComponent(e,"Fragment",Fragment,{},{default:e=>renderTemplate` <span>•</span> <span class="text-accent-600">${o.data.category}</span> `})}`} ${"compact"!==d&&renderTemplate`${renderComponent(e,"Fragment",Fragment,{},{default:e=>renderTemplate` <span>•</span> <span>${n} min read</span> `})}`} </div> <!-- Title --> <h3${addAttribute("font-semibold text-gray-900 mb-2 "+("featured"===d?"text-xl":"compact"===d?"text-base":"text-lg"),"class")}> <a${addAttribute(`/blog/${o.slug}`,"href")} class="hover:text-accent-600 transition-colors"> ${o.data.title} </a> </h3> <!-- Description --> <p${addAttribute("text-gray-600 mb-4 "+("compact"===d?"text-sm line-clamp-2":"line-clamp-3"),"class")}> ${o.data.description} </p> <!-- Tags (only for featured and default variants) --> ${"compact"!==d&&o.data.tags&&o.data.tags.length>0&&renderTemplate`<div class="flex flex-wrap gap-1 mb-4"> ${o.data.tags.slice(0,3).map((e=>renderTemplate`<span class="inline-block px-2 py-1 text-xs text-gray-600 bg-gray-100 rounded-full">
#${e} </span>`))} ${o.data.tags.length>3&&renderTemplate`<span class="inline-block px-2 py-1 text-xs text-gray-500">
+${o.data.tags.length-3} more
</span>`} </div>`} <!-- Read More Link --> <a${addAttribute(`/blog/${o.slug}`,"href")} class="inline-flex items-center text-accent-600 hover:text-accent-700 font-medium text-sm">
Read more
<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </a> <!-- Author (only for featured variant) --> ${"featured"===d&&o.data.author&&renderTemplate`<div class="flex items-center gap-2 mt-4 pt-4 border-t border-gray-100"> <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path> </svg> <span class="text-sm text-gray-600">By ${o.data.author}</span> </div>`} </div> </article>`}),"D:/code/image/polar-image-store/src/components/blog/BlogCard.astro",void 0);export{$$BlogCard as $};