globalThis.process??={},globalThis.process.env??={};import{o as objectType,h as dateType,n as numberType,i as arrayType,j as stringType,A as AstroError,U as UnknownContentCollectionError,a as createComponent,R as RenderUndefinedEntryError,u as unescapeHTML,r as renderTemplate,k as escape,l as renderUniqueStylesheet,p as renderScriptElement,q as createHeadAndContent,d as renderComponent}from"./astro/server_B4z0x52z.mjs";import{r as removeBase,i as isRemotePath,p as prependForwardSlash}from"./path_C-ZOwaTP.mjs";import{V as VALID_INPUT_FORMATS}from"./consts_Q4AsXsmN.mjs";import{u as unflatten}from"./parse_EttCPxrw.mjs";var e=e=>Object.prototype.toString.call(e),t=e=>ArrayBuffer.isView(e)&&!(e instanceof DataView),o=t=>"[object Date]"===e(t),n=t=>"[object RegExp]"===e(t),r=t=>"[object Error]"===e(t),s=t=>"[object Boolean]"===e(t),l=t=>"[object Number]"===e(t),i=t=>"[object String]"===e(t),c=Array.isArray,u=Object.getOwnPropertyDescriptor,a=Object.prototype.propertyIsEnumerable,f=Object.getOwnPropertySymbols,p=Object.prototype.hasOwnProperty,h=Object.keys;function d(e){const t=h(e),n=f(e);for(let o=0;o<n.length;o++)a.call(e,n[o])&&t.push(n[o]);return t}function b(e,t){return!u(e,t)?.writable}function y(e,a){if("object"==typeof e&&null!==e){let u;if(c(e))u=[];else if(o(e))u=new Date(e.getTime?e.getTime():e);else if(n(e))u=new RegExp(e);else if(r(e))u={message:e.message};else if(s(e)||l(e)||i(e))u=Object(e);else{if(t(e))return e.slice();u=Object.create(Object.getPrototypeOf(e))}const p=a.includeSymbols?d:h;for(const t of p(e))u[t]=e[t];return u}return e}var g={includeSymbols:!1,immutable:!1};function m(e,t,n=g){const o=[],r=[];let a=!0;const s=n.includeSymbols?d:h,i=!!n.immutable;return function e(l){const u=i?y(l,n):l,d={};let f=!0;const h={node:u,node_:l,path:[].concat(o),parent:r[r.length-1],parents:r,key:o[o.length-1],isRoot:0===o.length,level:o.length,circular:void 0,isLeaf:!1,notLeaf:!0,notRoot:!0,isFirst:!1,isLast:!1,update:function(e,t=!1){h.isRoot||(h.parent.node[h.key]=e),h.node=e,t&&(f=!1)},delete:function(e){delete h.parent.node[h.key],e&&(f=!1)},remove:function(e){c(h.parent.node)?h.parent.node.splice(h.key,1):delete h.parent.node[h.key],e&&(f=!1)},keys:null,before:function(e){d.before=e},after:function(e){d.after=e},pre:function(e){d.pre=e},post:function(e){d.post=e},stop:function(){a=!1},block:function(){f=!1}};if(!a)return h;function m(){if("object"==typeof h.node&&null!==h.node){h.keys&&h.node_===h.node||(h.keys=s(h.node)),h.isLeaf=0===h.keys.length;for(let e=0;e<r.length;e++)if(r[e].node_===l){h.circular=r[e];break}}else h.isLeaf=!0,h.keys=null;h.notLeaf=!h.isLeaf,h.notRoot=!h.isRoot}m();const g=t(h,h.node);if(void 0!==g&&h.update&&h.update(g),d.before&&d.before(h,h.node),!f)return h;if("object"==typeof h.node&&null!==h.node&&!h.circular){r.push(h),m();for(const[t,n]of Object.entries(h.keys??[])){o.push(n),d.pre&&d.pre(h,h.node[n],n);const r=e(h.node[n]);i&&p.call(h.node,n)&&!b(h.node,n)&&(h.node[n]=r.node),r.isLast=!!h.keys?.length&&+t==h.keys.length-1,r.isFirst=0==+t,d.post&&d.post(h,r),o.pop()}r.pop()}return d.after&&d.after(h,h.node),h}(e).node}var j=class{#e;#t;constructor(e,t=g){this.#e=e,this.#t=t}get(e){let t=this.#e;for(let n=0;t&&n<e.length;n++){const o=e[n];if(!p.call(t,o)||!this.#t.includeSymbols&&"symbol"==typeof o)return;t=t[o]}return t}has(e){let t=this.#e;for(let n=0;t&&n<e.length;n++){const o=e[n];if(!p.call(t,o)||!this.#t.includeSymbols&&"symbol"==typeof o)return!1;t=t[o]}return!0}set(e,t){let n=this.#e,o=0;for(o=0;o<e.length-1;o++){const t=e[o];p.call(n,t)||(n[t]={}),n=n[t]}return n[e[o]]=t,t}map(e){return m(this.#e,e,{immutable:!0,includeSymbols:!!this.#t.includeSymbols})}forEach(e){return this.#e=m(this.#e,e,this.#t),this.#e}reduce(e,t){const n=1===arguments.length;let o=n?this.#e:t;return this.forEach(((t,r)=>{t.isRoot&&n||(o=e(t,o,r))})),o}paths(){const e=[];return this.forEach((t=>{e.push(t.path)})),e}nodes(){const e=[];return this.forEach((t=>{e.push(t.node)})),e}clone(){const e=[],n=[],o=this.#t;return t(this.#e)?this.#e.slice():function t(r){for(let t=0;t<e.length;t++)if(e[t]===r)return n[t];if("object"==typeof r&&null!==r){const a=y(r,o);e.push(r),n.push(a);const s=o.includeSymbols?d:h;for(const e of s(r))a[e]=t(r[e]);return e.pop(),n.pop(),a}return r}(this.#e)}};class Node{value;next;constructor(e){this.value=e}}class Queue{#n;#o;#r;constructor(){this.clear()}enqueue(e){const t=new Node(e);this.#n?(this.#o.next=t,this.#o=t):(this.#n=t,this.#o=t),this.#r++}dequeue(){const e=this.#n;if(e)return this.#n=this.#n.next,this.#r--,e.value}peek(){if(this.#n)return this.#n.value}clear(){this.#n=void 0,this.#o=void 0,this.#r=0}get size(){return this.#r}*[Symbol.iterator](){let e=this.#n;for(;e;)yield e.value,e=e.next}*drain(){for(;this.#n;)yield this.dequeue()}}function pLimit(e){validateConcurrency(e);const t=new Queue;let n=0;const o=()=>{n<e&&t.size>0&&(t.dequeue()(),n++)},r=async(e,t,r)=>{const a=(async()=>e(...r))();t(a);try{await a}catch{}n--,o()},a=(a,...s)=>new Promise((i=>{((a,s,i)=>{new Promise((e=>{t.enqueue(e)})).then(r.bind(void 0,a,s,i)),(async()=>{await Promise.resolve(),n<e&&o()})()})(a,i,s)}));return Object.defineProperties(a,{activeCount:{get:()=>n},pendingCount:{get:()=>t.size},clearQueue:{value(){t.clear()}},concurrency:{get:()=>e,set(r){validateConcurrency(r),e=r,queueMicrotask((()=>{for(;n<e&&t.size>0;)o()}))}}}),a}function validateConcurrency(e){if(!Number.isInteger(e)&&e!==Number.POSITIVE_INFINITY||!(e>0))throw new TypeError("Expected `concurrency` to be a number from 1 and up")}const CONTENT_IMAGE_FLAG="astroContentImageFlag",IMAGE_IMPORT_PREFIX="__ASTRO_IMAGE_";function imageSrcToImportId(e,t){if(e=removeBase(e,"__ASTRO_IMAGE_"),isRemotePath(e))return;const n=e.split(".").at(-1)?.toLowerCase();if(!n||!VALID_INPUT_FORMATS.includes(n))return;const o=new URLSearchParams(CONTENT_IMAGE_FLAG);return t&&o.set("importer",t),`${e}?${o.toString()}`}class ImmutableDataStore{_collections=new Map;constructor(){this._collections=new Map}get(e,t){return this._collections.get(e)?.get(String(t))}entries(e){return[...(this._collections.get(e)??new Map).entries()]}values(e){return[...(this._collections.get(e)??new Map).values()]}keys(e){return[...(this._collections.get(e)??new Map).keys()]}has(e,t){const n=this._collections.get(e);return!!n&&n.has(String(t))}hasCollection(e){return this._collections.has(e)}collections(){return this._collections}static async fromModule(){try{const e=await import("./_astro_data-layer-content_C7QVfIUG.mjs");if(e.default instanceof Map)return ImmutableDataStore.fromMap(e.default);const t=unflatten(e.default);return ImmutableDataStore.fromMap(t)}catch{}return new ImmutableDataStore}static async fromMap(e){const t=new ImmutableDataStore;return t._collections=e,t}}function dataStoreSingleton(){let e;return{get:async()=>(e||(e=ImmutableDataStore.fromModule()),e),set:t=>{e=t}}}const globalDataStore=dataStoreSingleton(),__vite_import_meta_env__={ASSETS_PREFIX:void 0,BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,PUBLIC_SITE_URL:"http://infpik.store",SITE:"https://infpik.store",SSR:!0};function createCollectionToGlobResultMap({globResult:e,contentDir:t}){const n={};for(const o in e){const r=o.replace(new RegExp(`^${t}`),"").split("/");if(r.length<=1)continue;const a=r[0];n[a]??={},n[a][o]=e[o]}return n}function createGetCollection({contentCollectionToEntryMap:e,dataCollectionToEntryMap:t,getRenderEntryImport:n,cacheEntriesByCollection:o,liveCollections:r}){return async function(a,s){if(a in r)throw new AstroError({...UnknownContentCollectionError,message:`Collection "${a}" is a live collection. Use getLiveCollection() instead of getCollection().`});const i="function"==typeof s,c=await globalDataStore.get();let l;if(a in e)l="content";else{if(!(a in t)){if(c.hasCollection(a)){const{default:e}=await import("./content-assets_XqCgPAV2.mjs"),t=[];for(const n of c.values(a)){const o=updateImageReferencesInData(n.data,n.filePath,e);let r={...n,data:o,collection:a};r.legacyId&&(r=emulateLegacyEntry(r)),i&&!s(r)||t.push(r)}return t}return console.warn(`The collection ${JSON.stringify(a)} does not exist or is empty. Please check your content config file for errors.`),[]}l="data"}const u=Object.values("content"===l?e[a]:t[a]);let d=[];if(!Object.assign(__vite_import_meta_env__,{_:process.env._})?.DEV&&o.has(a))d=o.get(a);else{const e=pLimit(10);d=await Promise.all(u.map((t=>e((async()=>{const e=await t();return"content"===l?{id:e.id,slug:e.slug,body:e.body,collection:e.collection,data:e.data,render:async()=>render({collection:e.collection,id:e.id,renderEntryImport:await n(a,e.slug)})}:{id:e.id,collection:e.collection,data:e.data}}))))),o.set(a,d)}return i?d.filter(s):d.slice()}}function emulateLegacyEntry({legacyId:e,...t}){const n={...t,id:e,slug:t.id};return{...n,render:()=>renderEntry(n)}}objectType({tags:arrayType(stringType()).optional(),maxAge:numberType().optional(),lastModified:dateType().optional()});const CONTENT_LAYER_IMAGE_REGEX=/__ASTRO_IMAGE_="([^"]+)"/g;async function updateImageReferencesInBody(e,t){const{default:n}=await import("./content-assets_XqCgPAV2.mjs"),o=new Map,{getImage:r}=await import("./_astro_assets_dhiuug9q.mjs").then((e=>e._));for(const[a,s]of e.matchAll(CONTENT_LAYER_IMAGE_REGEX))try{const e=JSON.parse(s.replaceAll("&#x22;",'"'));let a;if(URL.canParse(e.src))a=await r(e);else{const s=imageSrcToImportId(e.src,t),i=n.get(s);if(!s||o.has(s)||!i)continue;a=await r({...e,src:i})}o.set(s,a)}catch{throw new Error(`Failed to parse image reference: ${s}`)}return e.replaceAll(CONTENT_LAYER_IMAGE_REGEX,((e,t)=>{const n=o.get(t);if(!n)return e;const{index:r,...a}=n.attributes;return Object.entries({...a,src:n.src,srcset:n.srcSet.attribute,...Object.assign(__vite_import_meta_env__,{_:process.env._}).DEV?{"data-image-component":"true"}:{}}).map((([e,t])=>t?`${e}="${escape(t)}"`:"")).join(" ")}))}function updateImageReferencesInData(e,t,n){return new j(e).map((function(e,o){if("string"==typeof o&&o.startsWith("__ASTRO_IMAGE_")){const r=o.replace("__ASTRO_IMAGE_",""),a=imageSrcToImportId(r,t);if(!a)return void e.update(r);const s=n?.get(a);s?e.update(s):e.update(r)}}))}async function renderEntry(e){if(!e)throw new AstroError(RenderUndefinedEntryError);if("render"in e&&!("legacyId"in e))return e.render();if(e.deferredRender)try{const{default:t}=await import("./content-modules_Bvq7llv8.mjs"),n=t.get(e.filePath);return render({collection:"",id:e.id,renderEntryImport:n})}catch(e){console.error(e)}const t=e?.rendered?.metadata?.imagePaths?.length&&e.filePath?await updateImageReferencesInBody(e.rendered.html,e.filePath):e?.rendered?.html;return{Content:createComponent((()=>renderTemplate`${unescapeHTML(t)}`)),headings:e?.rendered?.metadata?.headings??[],remarkPluginFrontmatter:e?.rendered?.metadata?.frontmatter??{}}}async function render({collection:e,id:t,renderEntryImport:n}){const o=new AstroError({...UnknownContentCollectionError,message:`Unexpected error while rendering ${String(e)} → ${String(t)}.`});if("function"!=typeof n)throw o;const r=await n();if(null==r||"object"!=typeof r)throw o;const{default:a}=r;if(isPropagatedAssetsModule(a)){const{collectedStyles:e,collectedLinks:n,collectedScripts:r,getMod:s}=a;if("function"!=typeof s)throw o;const i=await s();if(null==i||"object"!=typeof i)throw o;return{Content:createComponent({factory(o,a,s){let c="",l="",u="";Array.isArray(e)&&(c=e.map((e=>renderUniqueStylesheet(o,{type:"inline",content:e}))).join("")),Array.isArray(n)&&(l=n.map((e=>renderUniqueStylesheet(o,{type:"external",src:prependForwardSlash(e)}))).join("")),Array.isArray(r)&&(u=r.map((e=>renderScriptElement(e))).join(""));let d=a;return t.endsWith("mdx")&&(d={components:i.components??{},...a}),createHeadAndContent(unescapeHTML(c+l+u),renderTemplate`${renderComponent(o,"Content",i.Content,d,s)}`)},propagation:"self"}),headings:i.getHeadings?.()??[],remarkPluginFrontmatter:i.frontmatter??{}}}if(r.Content&&"function"==typeof r.Content)return{Content:r.Content,headings:r.getHeadings?.()??[],remarkPluginFrontmatter:r.frontmatter??{}};throw o}function isPropagatedAssetsModule(e){return"object"==typeof e&&null!=e&&"__astroPropagation"in e}const liveCollections={},contentDir="/src/content/",contentEntryGlob="",contentCollectionToEntryMap=createCollectionToGlobResultMap({globResult:"",contentDir:contentDir}),dataEntryGlob="",dataCollectionToEntryMap=createCollectionToGlobResultMap({globResult:"",contentDir:contentDir});createCollectionToGlobResultMap({globResult:{..."",...""},contentDir:contentDir});let lookupMap={};function createGlobLookup(e){return async(t,n)=>{const o=lookupMap[t]?.entries[n];if(o)return e[t][o]}}lookupMap={},new Set(Object.keys(lookupMap));const renderEntryGlob="",collectionToRenderEntryMap=createCollectionToGlobResultMap({globResult:"",contentDir:contentDir}),cacheEntriesByCollection=new Map,getCollection=createGetCollection({contentCollectionToEntryMap:contentCollectionToEntryMap,dataCollectionToEntryMap:dataCollectionToEntryMap,getRenderEntryImport:createGlobLookup(collectionToRenderEntryMap),cacheEntriesByCollection:cacheEntriesByCollection,liveCollections:liveCollections});export{getCollection as g};