import type { APIRoute } from 'astro';
import { searchPosts, getBlogCategories, getBlogTags } from '../../utils/blog';

export const prerender = false;

export const GET: APIRoute = async ({ url }) => {
  try {
    const searchParams = new URL(url).searchParams;
    const query = searchParams.get('q') || '';
    const type = searchParams.get('type') || 'posts'; // 'posts', 'categories', 'tags'
    const limit = parseInt(searchParams.get('limit') || '10');

    if (!query.trim()) {
      return new Response(JSON.stringify({ results: [] }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    let results: any[] = [];

    switch (type) {
      case 'posts':
        const posts = await searchPosts(query, limit);
        results = posts.map(post => ({
          type: 'post',
          title: post.data.title,
          description: post.data.description,
          url: `/blog/${post.slug}`,
          category: post.data.category,
          tags: post.data.tags,
          publishDate: post.data.publishDate.toISOString(),
          heroImage: post.data.heroImage,
        }));
        break;

      case 'categories':
        const categories = await getBlogCategories();
        const filteredCategories = categories.filter(category =>
          category.name.toLowerCase().includes(query.toLowerCase())
        ).slice(0, limit);
        
        results = filteredCategories.map(category => ({
          type: 'category',
          title: category.name,
          description: `${category.count} post${category.count === 1 ? '' : 's'} in this category`,
          url: `/blog/category/${category.slug}`,
          count: category.count,
        }));
        break;

      case 'tags':
        const tags = await getBlogTags();
        const filteredTags = tags.filter(tag =>
          tag.name.toLowerCase().includes(query.toLowerCase())
        ).slice(0, limit);
        
        results = filteredTags.map(tag => ({
          type: 'tag',
          title: `#${tag.name}`,
          description: `${tag.count} post${tag.count === 1 ? '' : 's'} with this tag`,
          url: `/blog/tag/${tag.slug}`,
          count: tag.count,
        }));
        break;

      case 'all':
        // Search across all types
        const [allPosts, allCategories, allTags] = await Promise.all([
          searchPosts(query, Math.floor(limit / 3)),
          getBlogCategories(),
          getBlogTags()
        ]);

        const postResults = allPosts.map(post => ({
          type: 'post',
          title: post.data.title,
          description: post.data.description,
          url: `/blog/${post.slug}`,
          category: post.data.category,
          tags: post.data.tags,
          publishDate: post.data.publishDate.toISOString(),
          heroImage: post.data.heroImage,
        }));

        const categoryResults = allCategories
          .filter(category => category.name.toLowerCase().includes(query.toLowerCase()))
          .slice(0, Math.floor(limit / 3))
          .map(category => ({
            type: 'category',
            title: category.name,
            description: `${category.count} post${category.count === 1 ? '' : 's'} in this category`,
            url: `/blog/category/${category.slug}`,
            count: category.count,
          }));

        const tagResults = allTags
          .filter(tag => tag.name.toLowerCase().includes(query.toLowerCase()))
          .slice(0, Math.floor(limit / 3))
          .map(tag => ({
            type: 'tag',
            title: `#${tag.name}`,
            description: `${tag.count} post${tag.count === 1 ? '' : 's'} with this tag`,
            url: `/blog/tag/${tag.slug}`,
            count: tag.count,
          }));

        results = [...postResults, ...categoryResults, ...tagResults];
        break;

      default:
        return new Response(JSON.stringify({ error: 'Invalid search type' }), {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        });
    }

    return new Response(JSON.stringify({ results }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });

  } catch (error) {
    console.error('Blog search error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};
