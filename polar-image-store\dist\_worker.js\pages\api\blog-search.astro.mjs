globalThis.process??={},globalThis.process.env??={};import{s as searchPosts,g as getBlogCategories,a as getBlogTags}from"../../chunks/blog_C5qimf1U.mjs";export{renderers}from"../../renderers.mjs";const prerender=!1,GET=async({url:e})=>{try{const t=new URL(e).searchParams,a=t.get("q")||"",s=t.get("type")||"posts",o=parseInt(t.get("limit")||"10");if(!a.trim())return new Response(JSON.stringify({results:[]}),{status:200,headers:{"Content-Type":"application/json"}});let r=[];switch(s){case"posts":r=(await searchPosts(a,o)).map((e=>({type:"post",title:e.data.title,description:e.data.description,url:`/blog/${e.slug}`,category:e.data.category,tags:e.data.tags,publishDate:e.data.publishDate.toISOString(),heroImage:e.data.heroImage})));break;case"categories":const e=await getBlogCategories();r=e.filter((e=>e.name.toLowerCase().includes(a.toLowerCase()))).slice(0,o).map((e=>({type:"category",title:e.name,description:`${e.count} post${1===e.count?"":"s"} in this category`,url:`/blog/category/${e.slug}`,count:e.count})));break;case"tags":const t=await getBlogTags();r=t.filter((e=>e.name.toLowerCase().includes(a.toLowerCase()))).slice(0,o).map((e=>({type:"tag",title:`#${e.name}`,description:`${e.count} post${1===e.count?"":"s"} with this tag`,url:`/blog/tag/${e.slug}`,count:e.count})));break;case"all":const[s,n,i]=await Promise.all([searchPosts(a,Math.floor(o/3)),getBlogCategories(),getBlogTags()]),l=s.map((e=>({type:"post",title:e.data.title,description:e.data.description,url:`/blog/${e.slug}`,category:e.data.category,tags:e.data.tags,publishDate:e.data.publishDate.toISOString(),heroImage:e.data.heroImage}))),g=n.filter((e=>e.name.toLowerCase().includes(a.toLowerCase()))).slice(0,Math.floor(o/3)).map((e=>({type:"category",title:e.name,description:`${e.count} post${1===e.count?"":"s"} in this category`,url:`/blog/category/${e.slug}`,count:e.count}))),c=i.filter((e=>e.name.toLowerCase().includes(a.toLowerCase()))).slice(0,Math.floor(o/3)).map((e=>({type:"tag",title:`#${e.name}`,description:`${e.count} post${1===e.count?"":"s"} with this tag`,url:`/blog/tag/${e.slug}`,count:e.count})));r=[...l,...g,...c];break;default:return new Response(JSON.stringify({error:"Invalid search type"}),{status:400,headers:{"Content-Type":"application/json"}})}return new Response(JSON.stringify({results:r}),{status:200,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Blog search error:",e),new Response(JSON.stringify({error:"Internal server error"}),{status:500,headers:{"Content-Type":"application/json"}})}},_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};