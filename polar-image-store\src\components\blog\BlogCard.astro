---
import type { CollectionEntry } from 'astro:content';

export interface Props {
  post: CollectionEntry<'blog'>;
  variant?: 'default' | 'featured' | 'compact';
}

const { post, variant = 'default' } = Astro.props;

// Calculate reading time (rough estimate: 200 words per minute)
const wordCount = post.body.split(/\s+/).length;
const readingTime = Math.ceil(wordCount / 200);

const cardClasses = {
  default: 'bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow',
  featured: 'bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow',
  compact: 'bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow'
};

const imageClasses = {
  default: 'w-full h-40 object-cover',
  featured: 'w-full h-48 object-cover',
  compact: 'w-full h-32 object-cover'
};

const contentClasses = {
  default: 'p-4',
  featured: 'p-6',
  compact: 'p-3'
};
---

<article class={cardClasses[variant]}>
  {post.data.heroImage && (
    <img
      src={post.data.heroImage}
      alt={post.data.title}
      class={imageClasses[variant]}
      loading="lazy"
    />
  )}
  
  <div class={contentClasses[variant]}>
    <!-- Meta Information -->
    <div class="flex items-center gap-2 text-sm text-gray-500 mb-2">
      <time datetime={post.data.publishDate.toISOString()}>
        {post.data.publishDate.toLocaleDateString('en-US', {
          year: variant === 'compact' ? '2-digit' : 'numeric',
          month: variant === 'compact' ? 'short' : 'long',
          day: 'numeric'
        })}
      </time>
      
      {post.data.category && (
        <>
          <span>•</span>
          <span class="text-accent-600">{post.data.category}</span>
        </>
      )}
      
      {variant !== 'compact' && (
        <>
          <span>•</span>
          <span>{readingTime} min read</span>
        </>
      )}
    </div>

    <!-- Title -->
    <h3 class={`font-semibold text-gray-900 mb-2 ${
      variant === 'featured' ? 'text-xl' : 
      variant === 'compact' ? 'text-base' : 'text-lg'
    }`}>
      <a 
        href={`/blog/${post.slug}`} 
        class="hover:text-accent-600 transition-colors"
      >
        {post.data.title}
      </a>
    </h3>

    <!-- Description -->
    <p class={`text-gray-600 mb-4 ${
      variant === 'compact' ? 'text-sm line-clamp-2' : 'line-clamp-3'
    }`}>
      {post.data.description}
    </p>

    <!-- Tags (only for featured and default variants) -->
    {variant !== 'compact' && post.data.tags && post.data.tags.length > 0 && (
      <div class="flex flex-wrap gap-1 mb-4">
        {post.data.tags.slice(0, 3).map((tag) => (
          <span class="inline-block px-2 py-1 text-xs text-gray-600 bg-gray-100 rounded-full">
            #{tag}
          </span>
        ))}
        {post.data.tags.length > 3 && (
          <span class="inline-block px-2 py-1 text-xs text-gray-500">
            +{post.data.tags.length - 3} more
          </span>
        )}
      </div>
    )}

    <!-- Read More Link -->
    <a
      href={`/blog/${post.slug}`}
      class="inline-flex items-center text-accent-600 hover:text-accent-700 font-medium text-sm"
    >
      Read more
      <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </a>

    <!-- Author (only for featured variant) -->
    {variant === 'featured' && post.data.author && (
      <div class="flex items-center gap-2 mt-4 pt-4 border-t border-gray-100">
        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <span class="text-sm text-gray-600">By {post.data.author}</span>
      </div>
    )}
  </div>
</article>
