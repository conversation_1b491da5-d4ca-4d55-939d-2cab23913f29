---
import { getBlogCategories, getBlogTags } from '../../utils/blog';

export interface Props {
  variant?: 'categories' | 'tags' | 'both';
  activeCategory?: string;
  activeTag?: string;
  maxItems?: number;
}

const { 
  variant = 'both', 
  activeCategory, 
  activeTag, 
  maxItems = 8 
} = Astro.props;

let categories: Array<{name: string, count: number, slug: string}> = [];
let tags: Array<{name: string, count: number, slug: string}> = [];

try {
  if (variant === 'categories' || variant === 'both') {
    categories = await getBlogCategories();
  }
  if (variant === 'tags' || variant === 'both') {
    tags = await getBlogTags();
  }
} catch (error) {
  console.error('Error fetching blog navigation data:', error);
}
---

<div class="blog-navigation">
  {(variant === 'categories' || variant === 'both') && categories.length > 0 && (
    <section class="mb-8">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
      <div class="overflow-x-auto scrollbar-hide">
        <div class="flex gap-2 pb-2 min-w-max">
          <!-- All Categories Link -->
          <a
            href="/blog"
            class={`category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap border ${
              !activeCategory
                ? 'bg-accent-500 text-white border-accent-500 shadow-md'
                : 'bg-white text-primary-900 border-primary-200 shadow-sm hover:bg-primary-50 hover:border-accent-500'
            }`}
          >
            All Posts
          </a>
          
          {categories.slice(0, maxItems).map((category) => (
            <a
              href={`/blog/category/${category.slug}`}
              class={`category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap border ${
                activeCategory === category.slug
                  ? 'bg-accent-500 text-white border-accent-500 shadow-md'
                  : 'bg-white text-primary-900 border-primary-200 shadow-sm hover:bg-primary-50 hover:border-accent-500'
              }`}
            >
              {category.name}
              <span class="text-xs opacity-75">({category.count})</span>
            </a>
          ))}
        </div>
      </div>
    </section>
  )}

  {(variant === 'tags' || variant === 'both') && tags.length > 0 && (
    <section class="mb-8">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Popular Tags</h3>
      <div class="overflow-x-auto scrollbar-hide">
        <div class="flex gap-2 pb-2 min-w-max">
          {tags.slice(0, maxItems).map((tag) => (
            <a
              href={`/blog/tag/${tag.slug}`}
              class={`tag-tab flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium transition-all whitespace-nowrap ${
                activeTag === tag.slug
                  ? 'bg-accent-100 text-accent-700 border border-accent-300'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              #{tag.name}
              <span class="text-xs opacity-75">({tag.count})</span>
            </a>
          ))}
        </div>
      </div>
    </section>
  )}
</div>

<style>
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .category-tab:hover,
  .tag-tab:hover {
    transform: translateY(-1px);
  }
</style>
