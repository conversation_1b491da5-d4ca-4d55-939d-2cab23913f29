---
import Layout from '../../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import BlogCard from '../../../components/blog/BlogCard.astro';
import StructuredData from '../../../components/StructuredData.astro';

export async function getStaticPaths() {
  const allPosts = await getCollection('blog');
  const publishedPosts = allPosts.filter(post => !post.data.draft);
  
  // Get all unique tags
  const allTags = publishedPosts.flatMap(post => post.data.tags || []);
  const uniqueTags = [...new Set(allTags)];
  
  return uniqueTags.map((tag) => ({
    params: { tag: tag.toLowerCase().replace(/\s+/g, '-') },
    props: { 
      tag,
      posts: publishedPosts.filter(post => post.data.tags?.includes(tag))
        .sort((a, b) => new Date(b.data.publishDate).getTime() - new Date(a.data.publishDate).getTime())
    },
  }));
}

const { tag, posts } = Astro.props;
const { tag: tagParam } = Astro.params;

const breadcrumbData = {
  itemListElement: [
    {
      "@type": "ListItem",
      position: 1,
      name: "Home",
      item: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}`
    },
    {
      "@type": "ListItem",
      position: 2,
      name: "Blog",
      item: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/blog`
    },
    {
      "@type": "ListItem",
      position: 3,
      name: `#${tag}`,
      item: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/blog/tag/${tagParam}`
    }
  ]
};
---

<Layout
  title={`#${tag} - InfPik Blog`}
  description={`Browse all blog posts tagged with ${tag}. Discover related insights, tutorials, and inspiration.`}
  canonical={`${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/blog/tag/${tagParam}`}
  type="website"
>
  <!-- Breadcrumb Structured Data -->
  <StructuredData type="BreadcrumbList" data={breadcrumbData} />

  <div class="w-full px-4 md:px-8 py-8">
    <!-- Breadcrumb Navigation -->
    <nav class="mb-8" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-2 text-sm text-gray-500">
        <li><a href="/" class="hover:text-accent-600">Home</a></li>
        <li><span class="mx-2">/</span></li>
        <li><a href="/blog" class="hover:text-accent-600">Blog</a></li>
        <li><span class="mx-2">/</span></li>
        <li class="text-gray-900 font-medium">#{tag}</li>
      </ol>
    </nav>

    <!-- Header -->
    <section class="text-center mb-12">
      <div class="mb-4">
        <span class="inline-block px-4 py-2 text-lg font-medium text-accent-700 bg-accent-100 rounded-full">
          #{tag}
        </span>
      </div>
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        Posts tagged with "{tag}"
      </h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        {posts.length} post{posts.length === 1 ? '' : 's'} with this tag
      </p>
    </section>

    <!-- Posts Grid -->
    {posts.length > 0 ? (
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {posts.map((post) => (
          <BlogCard post={post} variant="default" />
        ))}
      </div>
    ) : (
      <section class="text-center py-16">
        <div class="max-w-md mx-auto">
          <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z" />
          </svg>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">No posts found</h3>
          <p class="text-gray-600 mb-4">There are no posts with this tag yet.</p>
          <a
            href="/blog"
            class="inline-flex items-center px-4 py-2 bg-accent-600 text-white rounded-lg hover:bg-accent-700 transition-colors"
          >
            Browse all posts
          </a>
        </div>
      </section>
    )}

    <!-- Related Tags -->
    {posts.length > 0 && (
      <section class="mt-16 border-t border-gray-200 pt-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Tags</h2>
        <div class="flex flex-wrap gap-2">
          {[...new Set(posts.flatMap(post => post.data.tags || []).filter(t => t !== tag))].slice(0, 10).map((relatedTag) => (
            <a
              href={`/blog/tag/${relatedTag.toLowerCase().replace(/\s+/g, '-')}`}
              class="inline-block px-3 py-1 text-sm text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors"
            >
              #{relatedTag}
            </a>
          ))}
        </div>
      </section>
    )}

    <!-- Back to Blog -->
    <div class="mt-12 text-center">
      <a
        href="/blog"
        class="inline-flex items-center text-accent-600 hover:text-accent-700 font-medium"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
        Back to all posts
      </a>
    </div>
  </div>
</Layout>
