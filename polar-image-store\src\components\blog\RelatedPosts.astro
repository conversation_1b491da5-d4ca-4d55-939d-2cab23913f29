---
import type { CollectionEntry } from 'astro:content';
import { getRelatedPosts } from '../../utils/blog';
import BlogCard from './BlogCard.astro';

export interface Props {
  currentPost: CollectionEntry<'blog'>;
  limit?: number;
  variant?: 'default' | 'compact' | 'grid';
  title?: string;
}

const { 
  currentPost, 
  limit = 3, 
  variant = 'default',
  title = 'Related Posts'
} = Astro.props;

let relatedPosts: CollectionEntry<'blog'>[] = [];

try {
  relatedPosts = await getRelatedPosts(currentPost, limit);
} catch (error) {
  console.error('Error fetching related posts:', error);
}
---

{relatedPosts.length > 0 && (
  <section class="related-posts">
    <div class="mb-8">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
      <p class="text-gray-600">
        Discover more content related to this post
      </p>
    </div>

    {variant === 'grid' ? (
      <!-- Grid Layout -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {relatedPosts.map((post) => (
          <BlogCard post={post} variant="default" />
        ))}
      </div>
    ) : variant === 'compact' ? (
      <!-- Compact List Layout -->
      <div class="space-y-4">
        {relatedPosts.map((post) => (
          <article class="flex gap-4 p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
            {post.data.heroImage && (
              <img
                src={post.data.heroImage}
                alt={post.data.title}
                class="w-20 h-20 object-cover rounded-lg flex-shrink-0"
                loading="lazy"
              />
            )}
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 text-sm text-gray-500 mb-1">
                <time datetime={post.data.publishDate.toISOString()}>
                  {post.data.publishDate.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric'
                  })}
                </time>
                {post.data.category && (
                  <>
                    <span>•</span>
                    <span class="text-accent-600">{post.data.category}</span>
                  </>
                )}
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">
                <a href={`/blog/${post.slug}`} class="hover:text-accent-600 transition-colors">
                  {post.data.title}
                </a>
              </h3>
              <p class="text-gray-600 text-sm line-clamp-2">
                {post.data.description}
              </p>
            </div>
          </article>
        ))}
      </div>
    ) : (
      <!-- Default Card Layout -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        {relatedPosts.map((post) => (
          <BlogCard post={post} variant="default" />
        ))}
      </div>
    )}

    <!-- View More Link -->
    <div class="mt-8 text-center">
      <a
        href="/blog"
        class="inline-flex items-center px-6 py-3 bg-accent-600 text-white rounded-lg hover:bg-accent-700 transition-colors font-medium"
      >
        View all blog posts
        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </a>
    </div>
  </section>
)}

{relatedPosts.length === 0 && (
  <!-- No Related Posts -->
  <section class="related-posts text-center py-12">
    <div class="max-w-md mx-auto">
      <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <h3 class="text-xl font-semibold text-gray-900 mb-2">No related posts found</h3>
      <p class="text-gray-600 mb-6">
        Check out our latest blog posts for more great content.
      </p>
      <a
        href="/blog"
        class="inline-flex items-center px-6 py-3 bg-accent-600 text-white rounded-lg hover:bg-accent-700 transition-colors font-medium"
      >
        Browse all posts
        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </a>
    </div>
  </section>
)}

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
