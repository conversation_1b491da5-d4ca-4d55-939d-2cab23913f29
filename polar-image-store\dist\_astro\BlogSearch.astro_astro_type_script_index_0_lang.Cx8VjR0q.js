class a{input=null;resultsContainer=null;debounceTimer=null;isSearching=!1;constructor(){this.init()}init(){this.input=document.getElementById("blogSearchInput"),this.resultsContainer=document.getElementById("blogSearchResults"),this.input&&(this.input.addEventListener("input",t=>{const e=t.target.value;this.debouncedSearch(e)}),this.input.addEventListener("focus",()=>{this.input?.value.trim()&&this.showResults()}),document.addEventListener("click",t=>{!this.input?.contains(t.target)&&!this.resultsContainer?.contains(t.target)&&this.hideResults()}))}debouncedSearch(t){this.debounceTimer&&clearTimeout(this.debounceTimer),this.debounceTimer=window.setTimeout(()=>{this.performSearch(t)},300)}async performSearch(t){if(!t.trim()){this.hideResults();return}this.isSearching=!0,this.showLoadingState();try{const r=await(await fetch(`/api/blog-search?q=${encodeURIComponent(t)}&type=all&limit=8`)).json();r.results&&r.results.length>0?this.displayResults(r.results,t):this.displayNoResults(t)}catch(e){console.error("Blog search error:",e),this.displayError()}finally{this.isSearching=!1}}showLoadingState(){this.resultsContainer&&(this.resultsContainer.innerHTML=`
        <div class="p-4 text-center">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-accent-600 mx-auto"></div>
          <p class="text-sm text-gray-500 mt-2">Searching blog posts...</p>
        </div>
      `,this.showResults())}displayResults(t,e){if(!this.resultsContainer)return;const r=this.groupResultsByType(t);let i='<div class="p-2">';r.posts.length>0&&(i+='<div class="mb-4">',i+='<h4 class="text-sm font-semibold text-gray-700 px-2 py-1">Blog Posts</h4>',r.posts.forEach(s=>{i+=`
            <a href="${s.url}" class="block p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div class="flex items-start gap-3">
                ${s.heroImage?`
                  <img src="${s.heroImage}" alt="${s.title}" class="w-12 h-12 object-cover rounded-lg flex-shrink-0" />
                `:`
                  <div class="w-12 h-12 bg-gray-200 rounded-lg flex-shrink-0 flex items-center justify-center">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                `}
                <div class="flex-1 min-w-0">
                  <h5 class="text-sm font-medium text-gray-900 truncate">${this.highlightQuery(s.title,e)}</h5>
                  <p class="text-xs text-gray-500 mt-1 line-clamp-2">${this.highlightQuery(s.description,e)}</p>
                  <div class="flex items-center gap-2 mt-1">
                    ${s.category?`<span class="text-xs text-accent-600">${s.category}</span>`:""}
                    <span class="text-xs text-gray-400">${new Date(s.publishDate).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            </a>
          `}),i+="</div>"),r.categories.length>0&&(i+='<div class="mb-4">',i+='<h4 class="text-sm font-semibold text-gray-700 px-2 py-1">Categories</h4>',r.categories.forEach(s=>{i+=`
            <a href="${s.url}" class="block p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <div class="flex-1">
                  <h5 class="text-sm font-medium text-gray-900">${this.highlightQuery(s.title,e)}</h5>
                  <p class="text-xs text-gray-500">${s.description}</p>
                </div>
              </div>
            </a>
          `}),i+="</div>"),r.tags.length>0&&(i+="<div>",i+='<h4 class="text-sm font-semibold text-gray-700 px-2 py-1">Tags</h4>',r.tags.forEach(s=>{i+=`
            <a href="${s.url}" class="block p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                </div>
                <div class="flex-1">
                  <h5 class="text-sm font-medium text-gray-900">${this.highlightQuery(s.title,e)}</h5>
                  <p class="text-xs text-gray-500">${s.description}</p>
                </div>
              </div>
            </a>
          `}),i+="</div>"),i+="</div>",this.resultsContainer.innerHTML=i,this.showResults()}groupResultsByType(t){return{posts:t.filter(e=>e.type==="post"),categories:t.filter(e=>e.type==="category"),tags:t.filter(e=>e.type==="tag")}}displayNoResults(t){this.resultsContainer&&(this.resultsContainer.innerHTML=`
        <div class="p-6 text-center">
          <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="text-sm font-medium text-gray-900 mb-1">No blog posts found</h3>
          <p class="text-xs text-gray-500">Try searching with different keywords</p>
        </div>
      `,this.showResults())}displayError(){this.resultsContainer&&(this.resultsContainer.innerHTML=`
        <div class="p-6 text-center">
          <svg class="w-12 h-12 text-red-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 class="text-sm font-medium text-gray-900 mb-1">Search error</h3>
          <p class="text-xs text-gray-500">Please try again later</p>
        </div>
      `,this.showResults())}highlightQuery(t,e){if(!e.trim())return t;const r=new RegExp(`(${e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")})`,"gi");return t.replace(r,'<mark class="bg-yellow-200 px-1 rounded">$1</mark>')}showResults(){this.resultsContainer&&this.resultsContainer.classList.remove("hidden")}hideResults(){this.resultsContainer&&this.resultsContainer.classList.add("hidden")}}document.addEventListener("DOMContentLoaded",()=>{new a});
