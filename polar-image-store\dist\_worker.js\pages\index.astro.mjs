globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,m as maybeRenderHead,u as unescapeHTML,f as renderSlot,e as renderScript,r as renderTemplate,b as addAttribute,d as renderComponent,F as Fragment}from"../chunks/astro/server_B4z0x52z.mjs";import{$ as $$Layout}from"../chunks/Layout_Dwp3-WNN.mjs";import{$ as $$ProductCard}from"../chunks/ProductCard_MKLnFzX0.mjs";import{$ as $$StructuredData}from"../chunks/StructuredData_VefXz0ov.mjs";import{c as createPolarClient,t as transformPolarProduct,h as generateCategoriesWithCounts,i as generateTagsWithCounts}from"../chunks/polar_D7XkB6p_.mjs";export{renderers}from"../renderers.mjs";const $$Astro$2=createAstro("https://infpik.store"),$$Hero=createComponent((async(e,t,a)=>{const r=e.createAstro($$Astro$2,t,a);r.self=$$Hero;const{title:o,subtitle:i}=r.props;return renderTemplate`${maybeRenderHead()}<section class="relative flex flex-col justify-center items-center py-12 lg:py-20 bg-gradient-to-br from-white via-primary-50/30 to-accent-50/20 overflow-hidden" data-astro-cid-bbe6dxrz> <!-- Background decoration --> <div class="absolute inset-0 bg-grid-pattern opacity-5" data-astro-cid-bbe6dxrz></div> <div class="absolute top-20 right-20 w-72 h-72 bg-accent-200/20 rounded-full blur-3xl" data-astro-cid-bbe6dxrz></div> <div class="absolute bottom-20 left-20 w-96 h-96 bg-primary-200/20 rounded-full blur-3xl" data-astro-cid-bbe6dxrz></div> <div class="container relative" data-astro-cid-bbe6dxrz> <div class="max-w-4xl mx-auto" data-astro-cid-bbe6dxrz> <!-- 1. Main title --> <h1 class="text-3xl md:text-4xl lg:text-5xl xl:text-5xl font-bold text-primary-900 mb-8 leading-tight max-w-3xl mx-auto text-center" data-astro-cid-bbe6dxrz>${unescapeHTML(o)}</h1> <!-- 2. Category Navigation --> <div class="relative mb-8" data-astro-cid-bbe6dxrz> <div class="overflow-x-auto scrollbar-hide" id="categoryScroll" data-astro-cid-bbe6dxrz> <div class="flex gap-2 pb-2 min-w-max justify-center" data-astro-cid-bbe6dxrz> ${renderSlot(e,a["category-navigation"])} </div> </div> </div> <!-- 3. Search Bar --> <div class="relative mb-8" data-astro-cid-bbe6dxrz> <div class="max-w-md mx-auto" data-astro-cid-bbe6dxrz> <div class="relative" data-astro-cid-bbe6dxrz> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" data-astro-cid-bbe6dxrz> <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-bbe6dxrz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" data-astro-cid-bbe6dxrz></path> </svg> </div> <input type="text" id="heroSearchInput" placeholder="Search for images..." class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-white text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 shadow-sm" autocomplete="off" data-astro-cid-bbe6dxrz> <!-- Search results dropdown (hidden by default) --> <div id="heroSearchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto" data-astro-cid-bbe6dxrz> <!-- Search results will be populated here --> </div> </div> </div> </div> <!-- 4. Tag Navigation --> <div class="relative" data-astro-cid-bbe6dxrz> ${renderSlot(e,a["tag-navigation"])} </div> </div> </div> </section>  ${renderScript(e,"D:/code/image/polar-image-store/src/components/Hero.astro?astro&type=script&index=0&lang.ts")}`}),"D:/code/image/polar-image-store/src/components/Hero.astro",void 0),$$Astro$1=createAstro("https://infpik.store"),$$TagNavigation=createComponent(((e,t,a)=>{const r=e.createAstro($$Astro$1,t,a);r.self=$$TagNavigation;const{tags:o,activeTag:i="all",variant:s="default"}=r.props;return renderTemplate`${"hero"===s?renderTemplate`<!-- Hero variant - simple horizontal scroll like CategoryNavigation -->
  ${maybeRenderHead()}<div class="text-center" data-astro-cid-wy47okmw><!-- Scroll container --><div class="overflow-x-auto scrollbar-hide" id="tagScroll" data-astro-cid-wy47okmw><div class="flex gap-2 pb-2 min-w-max justify-center" data-astro-cid-wy47okmw>${o.map((e=>renderTemplate`<button${addAttribute("tag-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap border "+(i===e.id?"bg-accent-500 text-white border-accent-500 shadow-md":"bg-white text-primary-900 border-primary-200 shadow-sm hover:bg-primary-50 hover:border-accent-500"),"class")}${addAttribute(e.id,"data-tag")} data-astro-cid-wy47okmw>${e.name}</button>`))}</div></div></div>`:renderTemplate`<!-- Default variant - full section with background -->
  <section class="py-6 bg-primary-50 border-b border-primary-200" data-astro-cid-wy47okmw><div class="container" data-astro-cid-wy47okmw><div class="mb-4" data-astro-cid-wy47okmw><h3 class="text-lg font-semibold text-primary-900 mb-2" data-astro-cid-wy47okmw>Browse by Tags</h3><p class="text-sm text-primary-600" data-astro-cid-wy47okmw>Find specific content with detailed tags</p></div><!-- Tag Navigation --><!-- Scroll container --><div class="overflow-x-auto scrollbar-hide" id="tagScroll" data-astro-cid-wy47okmw><div class="flex gap-2 pb-2 min-w-max" data-astro-cid-wy47okmw>${o.map((e=>renderTemplate`<button${addAttribute("tag-tab flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium transition-all whitespace-nowrap "+(i===e.id?"bg-accent-600 text-white shadow-md":"bg-primary-50 text-primary-900 border border-primary-200 hover:bg-primary-100 hover:text-primary-900"),"class")}${addAttribute(e.id,"data-tag")} data-astro-cid-wy47okmw>${e.name}</button>`))}</div></div></div></section>`}${renderScript(e,"D:/code/image/polar-image-store/src/components/TagNavigation.astro?astro&type=script&index=0&lang.ts")}`}),"D:/code/image/polar-image-store/src/components/TagNavigation.astro",void 0),$$Astro=createAstro("https://infpik.store"),$$Index=createComponent((async(e,t,a)=>{const r=e.createAstro($$Astro,t,a);r.self=$$Index;let o=[],i=[],s=[],d=null;try{const e=r.locals?.runtime?.env,t=createPolarClient(e),a=e?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";if(a){const e=await t.products.list({organizationId:a,isArchived:!1}),r=e.result?.items||[];o=r.map(transformPolarProduct).filter((e=>null!==e)),i=generateCategoriesWithCounts(o),s=generateTagsWithCounts(o)}}catch(e){console.error("Error fetching featured products:",e),d="Failed to load featured products"}return renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:"InfPik - Premium 3D Illustration Images",description:"Premium 3D Illustration images for creative projects. High-quality, commercial-use digital assets available for instant download.",type:"website"},{default:async e=>renderTemplate`  ${renderComponent(e,"StructuredData",$$StructuredData,{type:"Organization",data:{}})}  ${renderComponent(e,"StructuredData",$$StructuredData,{type:"WebSite",data:{}})}  ${renderComponent(e,"Hero",$$Hero,{title:"Premium. Passion. Creativity."},{"category-navigation":async e=>renderTemplate`${renderComponent(e,"Fragment",Fragment,{slot:"category-navigation"},{default:async e=>renderTemplate`${i.slice(0,6).map((e=>renderTemplate`${maybeRenderHead()}<button${addAttribute("category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap border "+("all"===e.id?"bg-accent-500 text-white border-accent-500 shadow-md":"bg-white text-primary-900 border-primary-200 shadow-sm hover:bg-primary-50 hover:border-accent-500"),"class")}${addAttribute(e.id,"data-category")}> ${e.name} </button>`))}`})}`,"tag-navigation":async e=>renderTemplate`${renderComponent(e,"Fragment",Fragment,{slot:"tag-navigation"},{default:async e=>renderTemplate` ${renderComponent(e,"TagNavigation",$$TagNavigation,{tags:s.slice(1,11),activeTag:"all",variant:"hero"})} `})}`})}  <section class="py-16 bg-white"> <div class="w-full px-4 md:px-8"> <div class="text-center mb-12"> <h2 class="text-3xl md:text-4xl font-bold text-primary-900 mb-4">Featured Collections</h2> <p class="text-lg text-primary-600 max-w-2xl mx-auto">
Discover our most popular 3D Illustration images
</p> </div> ${d?renderTemplate`<div class="text-center py-16"> <div class="inline-flex items-center gap-3 text-warning-600 bg-warning-50 px-6 py-4 rounded-xl border border-warning-200"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path> </svg> <span class="font-medium">${d}</span> </div> </div>`:0===o.length?renderTemplate`<div class="text-center py-16"> <div class="inline-flex items-center gap-3 text-primary-600"> <svg class="animate-spin w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path> </svg> <span>Loading featured products...</span> </div> </div>`:renderTemplate`<div id="products-grid" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6"> ${o.slice(0,16).map(((t,a)=>renderTemplate`<div class="product-item"${addAttribute(t.category||"uncategorized","data-category")}${addAttribute(t.name.toLowerCase(),"data-name")}${addAttribute(t.description.toLowerCase(),"data-description")}${addAttribute(t.tags?.join(",").toLowerCase()||"","data-tags")}${addAttribute(a<16?"true":"false","data-featured")}> ${renderComponent(e,"ProductCard",$$ProductCard,{product:t})} </div>`))} </div>`} ${o.length>0&&renderTemplate`<div class="text-center mt-12"> <a href="/products" id="view-all-products-btn" class="btn-primary text-lg px-8 py-4">
View All Collections
<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path> </svg> </a> </div>`} </div> </section>  <section class="py-16 bg-primary-50"> <div class="container"> <div class="text-center mb-12"> <h2 class="text-3xl md:text-4xl font-bold text-primary-900 mb-4">Why Choose Our Images?</h2> <p class="text-lg text-primary-600 max-w-2xl mx-auto">
Professional quality, instant access, and commercial licensing for all your creative projects
</p> </div> <div class="grid grid-cols-1 md:grid-cols-3 gap-8"> <div class="text-center p-8 bg-white rounded-3xl border border-primary-100 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300"> <div class="w-16 h-16 bg-gradient-to-br from-accent-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-6"> <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path> </svg> </div> <h3 class="text-xl font-bold text-primary-900 mb-4">Premium Quality</h3> <p class="text-primary-600 leading-relaxed">Professional-grade digital images in ultra-high resolution for stunning results</p> </div> <div class="text-center p-8 bg-white rounded-3xl border border-primary-100 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300"> <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6"> <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path> </svg> </div> <h3 class="text-xl font-bold text-primary-900 mb-4">Instant Access</h3> <p class="text-primary-600 leading-relaxed">Download your images immediately after purchase with secure, lifetime access</p> </div> <div class="text-center p-8 bg-white rounded-3xl border border-primary-100 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300"> <div class="w-16 h-16 bg-gradient-to-br from-warning-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6"> <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path> </svg> </div> <h3 class="text-xl font-bold text-primary-900 mb-4">Commercial License</h3> <p class="text-primary-600 leading-relaxed">Full commercial rights included - use for personal and business projects without limits</p> </div> </div> </div> </section> `})} ${renderScript(e,"D:/code/image/polar-image-store/src/pages/index.astro?astro&type=script&index=0&lang.ts")}`}),"D:/code/image/polar-image-store/src/pages/index.astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/index.astro",$$url="",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$Index,file:$$file,url:""},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};