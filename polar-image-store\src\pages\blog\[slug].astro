---
import Layout from '../../layouts/Layout.astro';
import { getCollection, type CollectionEntry } from 'astro:content';
import StructuredData from '../../components/StructuredData.astro';

export async function getStaticPaths() {
  const posts = await getCollection('blog');
  return posts
    .filter(post => !post.data.draft)
    .map((post) => ({
      params: { slug: post.slug },
      props: post,
    }));
}

type Props = CollectionEntry<'blog'>;

const post = Astro.props;
const { Content } = await post.render();

// Get related posts based on category and tags
const allPosts = await getCollection('blog');
const relatedPosts = allPosts
  .filter(p => 
    p.slug !== post.slug && 
    !p.data.draft &&
    (p.data.category === post.data.category || 
     p.data.tags?.some(tag => post.data.tags?.includes(tag)))
  )
  .slice(0, 3);

// Calculate reading time (rough estimate: 200 words per minute)
const wordCount = post.body.split(/\s+/).length;
const readingTime = Math.ceil(wordCount / 200);

// Structured data for article
const articleData = {
  headline: post.data.title,
  description: post.data.description,
  image: post.data.heroImage ? [post.data.heroImage] : [],
  datePublished: post.data.publishDate.toISOString(),
  dateModified: post.data.updatedDate?.toISOString() || post.data.publishDate.toISOString(),
  author: {
    "@type": "Person",
    name: post.data.author || "InfPik Team"
  },
  publisher: {
    "@type": "Organization",
    name: "InfPik",
    logo: {
      "@type": "ImageObject",
      url: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/logo.svg`
    }
  },
  mainEntityOfPage: {
    "@type": "WebPage",
    "@id": `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/blog/${post.slug}`
  }
};

const breadcrumbData = {
  itemListElement: [
    {
      "@type": "ListItem",
      position: 1,
      name: "Home",
      item: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}`
    },
    {
      "@type": "ListItem",
      position: 2,
      name: "Blog",
      item: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/blog`
    },
    {
      "@type": "ListItem",
      position: 3,
      name: post.data.title,
      item: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/blog/${post.slug}`
    }
  ]
};
---

<Layout
  title={`${post.data.title} - InfPik Blog`}
  description={post.data.description}
  image={post.data.heroImage}
  canonical={`${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/blog/${post.slug}`}
  type="article"
>
  <!-- Article Structured Data -->
  <StructuredData type="Article" data={articleData} />
  
  <!-- Breadcrumb Structured Data -->
  <StructuredData type="BreadcrumbList" data={breadcrumbData} />

  <article class="w-full px-4 md:px-8 py-8">
    <!-- Breadcrumb Navigation -->
    <nav class="mb-8" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-2 text-sm text-gray-500">
        <li><a href="/" class="hover:text-accent-600">Home</a></li>
        <li><span class="mx-2">/</span></li>
        <li><a href="/blog" class="hover:text-accent-600">Blog</a></li>
        <li><span class="mx-2">/</span></li>
        <li class="text-gray-900 font-medium">{post.data.title}</li>
      </ol>
    </nav>

    <!-- Article Header -->
    <header class="mb-8">
      {post.data.category && (
        <div class="mb-4">
          <span class="inline-block px-3 py-1 text-sm font-medium text-accent-700 bg-accent-100 rounded-full">
            {post.data.category}
          </span>
        </div>
      )}
      
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        {post.data.title}
      </h1>
      
      <p class="text-xl text-gray-600 mb-6">
        {post.data.description}
      </p>

      <div class="flex items-center gap-4 text-sm text-gray-500">
        <div class="flex items-center gap-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <time datetime={post.data.publishDate.toISOString()}>
            {post.data.publishDate.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </time>
        </div>
        
        <div class="flex items-center gap-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{readingTime} min read</span>
        </div>

        {post.data.author && (
          <div class="flex items-center gap-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span>By {post.data.author}</span>
          </div>
        )}
      </div>
    </header>

    <!-- Hero Image -->
    {post.data.heroImage && (
      <div class="mb-8">
        <img
          src={post.data.heroImage}
          alt={post.data.title}
          class="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
          loading="eager"
        />
      </div>
    )}

    <!-- Article Content -->
    <div class="prose prose-lg max-w-none mb-12">
      <Content />
    </div>

    <!-- Tags -->
    {post.data.tags && post.data.tags.length > 0 && (
      <div class="mb-12">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
        <div class="flex flex-wrap gap-2">
          {post.data.tags.map((tag) => (
            <a
              href={`/blog/tag/${tag}`}
              class="inline-block px-3 py-1 text-sm text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors"
            >
              #{tag}
            </a>
          ))}
        </div>
      </div>
    )}

    <!-- Related Posts -->
    {relatedPosts.length > 0 && (
      <section class="border-t border-gray-200 pt-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Related Posts</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          {relatedPosts.map((relatedPost) => (
            <article class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              {relatedPost.data.heroImage && (
                <img
                  src={relatedPost.data.heroImage}
                  alt={relatedPost.data.title}
                  class="w-full h-40 object-cover"
                  loading="lazy"
                />
              )}
              <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                  <a href={`/blog/${relatedPost.slug}`} class="hover:text-accent-600 transition-colors">
                    {relatedPost.data.title}
                  </a>
                </h3>
                <p class="text-gray-600 text-sm line-clamp-2">
                  {relatedPost.data.description}
                </p>
              </div>
            </article>
          ))}
        </div>
      </section>
    )}
  </article>
</Layout>

<style>
  .prose {
    color: rgb(55 65 81);
    line-height: 1.625;
  }

  .prose h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: rgb(17 24 39);
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .prose h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: rgb(17 24 39);
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .prose p {
    margin-bottom: 1rem;
  }

  .prose ul, .prose ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  .prose li {
    margin-bottom: 0.5rem;
  }

  .prose blockquote {
    border-left: 4px solid rgb(99 102 241);
    padding-left: 1rem;
    font-style: italic;
    color: rgb(75 85 99);
    margin: 1.5rem 0;
  }

  .prose code {
    background-color: rgb(243 244 246);
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  }

  .prose pre {
    background-color: rgb(17 24 39);
    color: rgb(243 244 246);
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1.5rem 0;
  }

  .prose pre code {
    background-color: transparent;
    padding: 0;
  }

  .prose img {
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    margin: 1.5rem 0;
  }

  .prose a {
    color: rgb(99 102 241);
    text-decoration: underline;
  }

  .prose a:hover {
    color: rgb(79 70 229);
  }
</style>
