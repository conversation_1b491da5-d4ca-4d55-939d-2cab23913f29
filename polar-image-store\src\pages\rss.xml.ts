import type { APIRoute } from 'astro';
import { getCollection } from 'astro:content';
import { generateExcerpt } from '../utils/blog';

export const prerender = false;

export const GET: APIRoute = async () => {
  try {
    // Get all published blog posts
    const allPosts = await getCollection('blog');
    const publishedPosts = allPosts
      .filter(post => !post.data.draft)
      .sort((a, b) => new Date(b.data.publishDate).getTime() - new Date(a.data.publishDate).getTime())
      .slice(0, 20); // Limit to 20 most recent posts

    const siteUrl = import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store';
    const currentDate = new Date().toUTCString();

    // Generate RSS XML
    const rss = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom" xmlns:content="http://purl.org/rss/1.0/modules/content/">
  <channel>
    <title>InfPik Blog</title>
    <description>Discover insights, tutorials, and inspiration about 3D illustration, design trends, and creative workflows.</description>
    <link>${siteUrl}/blog</link>
    <atom:link href="${siteUrl}/rss.xml" rel="self" type="application/rss+xml"/>
    <language>en-us</language>
    <lastBuildDate>${currentDate}</lastBuildDate>
    <managingEditor><EMAIL> (InfPik Team)</managingEditor>
    <webMaster><EMAIL> (InfPik Team)</webMaster>
    <generator>Astro</generator>
    <image>
      <url>${siteUrl}/logo.svg</url>
      <title>InfPik Blog</title>
      <link>${siteUrl}/blog</link>
      <width>144</width>
      <height>144</height>
    </image>
${publishedPosts.map(post => {
  const postUrl = `${siteUrl}/blog/${post.slug}`;
  const pubDate = new Date(post.data.publishDate).toUTCString();
  const excerpt = generateExcerpt(post.body, 200);
  
  // Clean content for RSS (remove markdown and HTML)
  const cleanContent = post.body
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/`[^`]*`/g, '') // Remove inline code
    .replace(/!\[.*?\]\(.*?\)/g, '') // Remove images
    .replace(/\[([^\]]*)\]\([^)]*\)/g, '$1') // Convert links to text
    .replace(/[#*_~`]/g, '') // Remove markdown formatting
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim()
    .substring(0, 500) + '...';

  return `    <item>
      <title><![CDATA[${post.data.title}]]></title>
      <description><![CDATA[${post.data.description}]]></description>
      <content:encoded><![CDATA[${cleanContent}]]></content:encoded>
      <link>${postUrl}</link>
      <guid isPermaLink="true">${postUrl}</guid>
      <pubDate>${pubDate}</pubDate>
      ${post.data.author ? `<author><EMAIL> (${post.data.author})</author>` : ''}
      ${post.data.category ? `<category><![CDATA[${post.data.category}]]></category>` : ''}
      ${post.data.tags ? post.data.tags.map(tag => `<category><![CDATA[${tag}]]></category>`).join('\n      ') : ''}
      ${post.data.heroImage ? `<enclosure url="${post.data.heroImage}" type="image/jpeg"/>` : ''}
    </item>`;
}).join('\n')}
  </channel>
</rss>`;

    return new Response(rss, {
      headers: {
        'Content-Type': 'application/rss+xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });

  } catch (error) {
    console.error('Error generating RSS feed:', error);
    return new Response('Error generating RSS feed', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  }
};
