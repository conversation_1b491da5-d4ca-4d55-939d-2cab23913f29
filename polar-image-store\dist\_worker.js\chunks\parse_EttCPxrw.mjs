function encode64(e){const r=new DataView(e);let t="";for(let n=0;n<e.byteLength;n++)t+=String.fromCharCode(r.getUint8(n));return binaryToAscii(t)}function decode64(e){const r=asciiToBinary(e),t=new ArrayBuffer(r.length),n=new DataView(t);for(let e=0;e<t.byteLength;e++)n.setUint8(e,r.charCodeAt(e));return t}globalThis.process??={},globalThis.process.env??={};const KEY_STRING="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function asciiToBinary(e){e.length%4==0&&(e=e.replace(/==?$/,""));let r="",t=0,n=0;for(let a=0;a<e.length;a++)t<<=6,t|=KEY_STRING.indexOf(e[a]),n+=6,24===n&&(r+=String.fromCharCode((16711680&t)>>16),r+=String.fromCharCode((65280&t)>>8),r+=String.fromCharCode(255&t),t=n=0);return 12===n?(t>>=4,r+=String.fromCharCode(t)):18===n&&(t>>=2,r+=String.fromCharCode((65280&t)>>8),r+=String.fromCharCode(255&t)),r}function binaryToAscii(e){let r="";for(let t=0;t<e.length;t+=3){const n=[void 0,void 0,void 0,void 0];n[0]=e.charCodeAt(t)>>2,n[1]=(3&e.charCodeAt(t))<<4,e.length>t+1&&(n[1]|=e.charCodeAt(t+1)>>4,n[2]=(15&e.charCodeAt(t+1))<<2),e.length>t+2&&(n[2]|=e.charCodeAt(t+2)>>6,n[3]=63&e.charCodeAt(t+2));for(let e=0;e<n.length;e++)void 0===n[e]?r+="=":r+=KEY_STRING[n[e]]}return r}const UNDEFINED=-1,HOLE=-2,NAN=-3,POSITIVE_INFINITY=-4,NEGATIVE_INFINITY=-5,NEGATIVE_ZERO=-6;function parse(e,r){return unflatten(JSON.parse(e),r)}function unflatten(e,r){if("number"==typeof e)return a(e,!0);if(!Array.isArray(e)||0===e.length)throw new Error("Invalid input");const t=e,n=Array(t.length);function a(e,o=!1){if(-1===e)return;if(-3===e)return NaN;if(-4===e)return 1/0;if(-5===e)return-1/0;if(-6===e)return-0;if(o)throw new Error("Invalid input");if(e in n)return n[e];const i=t[e];if(i&&"object"==typeof i)if(Array.isArray(i))if("string"==typeof i[0]){const t=i[0],o=r?.[t];if(o)return n[e]=o(a(i[1]));switch(t){case"Date":n[e]=new Date(i[1]);break;case"Set":const r=new Set;n[e]=r;for(let e=1;e<i.length;e+=1)r.add(a(i[e]));break;case"Map":const o=new Map;n[e]=o;for(let e=1;e<i.length;e+=2)o.set(a(i[e]),a(i[e+1]));break;case"RegExp":n[e]=new RegExp(i[1],i[2]);break;case"Object":n[e]=Object(i[1]);break;case"BigInt":n[e]=BigInt(i[1]);break;case"null":const c=Object.create(null);n[e]=c;for(let e=1;e<i.length;e+=2)c[i[e]]=a(i[e+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const r=new(0,globalThis[t])(decode64(i[1]));n[e]=r;break}case"ArrayBuffer":{const r=decode64(i[1]);n[e]=r;break}default:throw new Error(`Unknown type ${t}`)}}else{const r=new Array(i.length);n[e]=r;for(let e=0;e<i.length;e+=1){const t=i[e];-2!==t&&(r[e]=a(t))}}else{const r={};n[e]=r;for(const e in i){const t=i[e];r[e]=a(t)}}else n[e]=i;return n[e]}return a(0)}export{HOLE as H,NAN as N,POSITIVE_INFINITY as P,UNDEFINED as U,NEGATIVE_INFINITY as a,NEGATIVE_ZERO as b,encode64 as e,parse as p,unflatten as u};