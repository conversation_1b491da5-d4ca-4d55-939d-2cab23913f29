---
// BlogSearch component - Integrates blog search into existing search system
export interface Props {
  placeholder?: string;
  variant?: 'inline' | 'modal';
}

const { 
  placeholder = "Search blog posts...", 
  variant = 'inline' 
} = Astro.props;
---

{variant === 'inline' ? (
  <!-- Inline Blog Search -->
  <div class="blog-search-container">
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      <input
        type="text"
        id="blogSearchInput"
        placeholder={placeholder}
        class="block w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200"
      />
    </div>
    
    <!-- Search Results Container -->
    <div id="blogSearchResults" class="hidden absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto">
      <!-- Results will be populated by JavaScript -->
    </div>
  </div>
) : (
  <!-- Modal Integration - Add blog search tab to existing SearchModal -->
  <div class="blog-search-modal-integration">
    <!-- This will be integrated into the existing SearchModal -->
  </div>
)}

<script>
  class BlogSearch {
    private input: HTMLInputElement | null = null;
    private resultsContainer: HTMLElement | null = null;
    private debounceTimer: number | null = null;
    private isSearching: boolean = false;

    constructor() {
      this.init();
    }

    private init() {
      this.input = document.getElementById('blogSearchInput') as HTMLInputElement;
      this.resultsContainer = document.getElementById('blogSearchResults');

      if (this.input) {
        this.input.addEventListener('input', (e) => {
          const query = (e.target as HTMLInputElement).value;
          this.debouncedSearch(query);
        });

        this.input.addEventListener('focus', () => {
          if (this.input?.value.trim()) {
            this.showResults();
          }
        });

        // Hide results when clicking outside
        document.addEventListener('click', (e) => {
          if (!this.input?.contains(e.target as Node) && 
              !this.resultsContainer?.contains(e.target as Node)) {
            this.hideResults();
          }
        });
      }
    }

    private debouncedSearch(query: string) {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      this.debounceTimer = window.setTimeout(() => {
        this.performSearch(query);
      }, 300);
    }

    private async performSearch(query: string) {
      if (!query.trim()) {
        this.hideResults();
        return;
      }

      this.isSearching = true;
      this.showLoadingState();

      try {
        const response = await fetch(`/api/blog-search?q=${encodeURIComponent(query)}&type=all&limit=8`);
        const data = await response.json();

        if (data.results && data.results.length > 0) {
          this.displayResults(data.results, query);
        } else {
          this.displayNoResults(query);
        }
      } catch (error) {
        console.error('Blog search error:', error);
        this.displayError();
      } finally {
        this.isSearching = false;
      }
    }

    private showLoadingState() {
      if (!this.resultsContainer) return;

      this.resultsContainer.innerHTML = `
        <div class="p-4 text-center">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-accent-600 mx-auto"></div>
          <p class="text-sm text-gray-500 mt-2">Searching blog posts...</p>
        </div>
      `;
      this.showResults();
    }

    private displayResults(results: any[], query: string) {
      if (!this.resultsContainer) return;

      const groupedResults = this.groupResultsByType(results);
      
      let html = '<div class="p-2">';

      // Display posts
      if (groupedResults.posts.length > 0) {
        html += '<div class="mb-4">';
        html += '<h4 class="text-sm font-semibold text-gray-700 px-2 py-1">Blog Posts</h4>';
        groupedResults.posts.forEach(post => {
          html += `
            <a href="${post.url}" class="block p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div class="flex items-start gap-3">
                ${post.heroImage ? `
                  <img src="${post.heroImage}" alt="${post.title}" class="w-12 h-12 object-cover rounded-lg flex-shrink-0" />
                ` : `
                  <div class="w-12 h-12 bg-gray-200 rounded-lg flex-shrink-0 flex items-center justify-center">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                `}
                <div class="flex-1 min-w-0">
                  <h5 class="text-sm font-medium text-gray-900 truncate">${this.highlightQuery(post.title, query)}</h5>
                  <p class="text-xs text-gray-500 mt-1 line-clamp-2">${this.highlightQuery(post.description, query)}</p>
                  <div class="flex items-center gap-2 mt-1">
                    ${post.category ? `<span class="text-xs text-accent-600">${post.category}</span>` : ''}
                    <span class="text-xs text-gray-400">${new Date(post.publishDate).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            </a>
          `;
        });
        html += '</div>';
      }

      // Display categories
      if (groupedResults.categories.length > 0) {
        html += '<div class="mb-4">';
        html += '<h4 class="text-sm font-semibold text-gray-700 px-2 py-1">Categories</h4>';
        groupedResults.categories.forEach(category => {
          html += `
            <a href="${category.url}" class="block p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <div class="flex-1">
                  <h5 class="text-sm font-medium text-gray-900">${this.highlightQuery(category.title, query)}</h5>
                  <p class="text-xs text-gray-500">${category.description}</p>
                </div>
              </div>
            </a>
          `;
        });
        html += '</div>';
      }

      // Display tags
      if (groupedResults.tags.length > 0) {
        html += '<div>';
        html += '<h4 class="text-sm font-semibold text-gray-700 px-2 py-1">Tags</h4>';
        groupedResults.tags.forEach(tag => {
          html += `
            <a href="${tag.url}" class="block p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                </div>
                <div class="flex-1">
                  <h5 class="text-sm font-medium text-gray-900">${this.highlightQuery(tag.title, query)}</h5>
                  <p class="text-xs text-gray-500">${tag.description}</p>
                </div>
              </div>
            </a>
          `;
        });
        html += '</div>';
      }

      html += '</div>';
      this.resultsContainer.innerHTML = html;
      this.showResults();
    }

    private groupResultsByType(results: any[]) {
      return {
        posts: results.filter(r => r.type === 'post'),
        categories: results.filter(r => r.type === 'category'),
        tags: results.filter(r => r.type === 'tag')
      };
    }

    private displayNoResults(query: string) {
      if (!this.resultsContainer) return;

      this.resultsContainer.innerHTML = `
        <div class="p-6 text-center">
          <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="text-sm font-medium text-gray-900 mb-1">No blog posts found</h3>
          <p class="text-xs text-gray-500">Try searching with different keywords</p>
        </div>
      `;
      this.showResults();
    }

    private displayError() {
      if (!this.resultsContainer) return;

      this.resultsContainer.innerHTML = `
        <div class="p-6 text-center">
          <svg class="w-12 h-12 text-red-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 class="text-sm font-medium text-gray-900 mb-1">Search error</h3>
          <p class="text-xs text-gray-500">Please try again later</p>
        </div>
      `;
      this.showResults();
    }

    private highlightQuery(text: string, query: string): string {
      if (!query.trim()) return text;
      
      const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
      return text.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
    }

    private showResults() {
      if (this.resultsContainer) {
        this.resultsContainer.classList.remove('hidden');
      }
    }

    private hideResults() {
      if (this.resultsContainer) {
        this.resultsContainer.classList.add('hidden');
      }
    }
  }

  // Initialize blog search when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new BlogSearch();
  });
</script>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
