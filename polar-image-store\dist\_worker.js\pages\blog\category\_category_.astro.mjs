globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,d as renderComponent,r as renderTemplate,m as maybeRenderHead}from"../../../chunks/astro/server_B4z0x52z.mjs";import{$ as $$Layout}from"../../../chunks/Layout_Dwp3-WNN.mjs";import{g as getCollection}from"../../../chunks/_astro_content_3f_fRm09.mjs";import{$ as $$BlogCard}from"../../../chunks/BlogCard_BQ61Esuo.mjs";import{$ as $$StructuredData}from"../../../chunks/StructuredData_VefXz0ov.mjs";export{renderers}from"../../../renderers.mjs";const $$Astro=createAstro("https://infpik.store");async function getStaticPaths(){const e=(await getCollection("blog")).filter((e=>!e.data.draft));return[...new Set(e.map((e=>e.data.category)).filter(Boolean))].map((t=>({params:{category:t.toLowerCase().replace(/\s+/g,"-")},props:{category:t,posts:e.filter((e=>e.data.category===t)).sort(((e,t)=>new Date(t.data.publishDate).getTime()-new Date(e.data.publishDate).getTime()))}})))}const $$category=createComponent((async(e,t,a)=>{const o=e.createAstro($$Astro,t,a);o.self=$$category;const{category:r,posts:s}=o.props,{category:l}=o.params,n={itemListElement:[{"@type":"ListItem",position:1,name:"Home",item:"http://infpik.store"},{"@type":"ListItem",position:2,name:"Blog",item:"http://infpik.store/blog"},{"@type":"ListItem",position:3,name:r,item:`http://infpik.store/blog/category/${l}`}]};return renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:`${r} - InfPik Blog`,description:`Browse all blog posts in the ${r} category. Discover insights, tutorials, and inspiration.`,canonical:`http://infpik.store/blog/category/${l}`,type:"website"},{default:async e=>renderTemplate`  ${renderComponent(e,"StructuredData",$$StructuredData,{type:"BreadcrumbList",data:n})} ${maybeRenderHead()}<div class="w-full px-4 md:px-8 py-8"> <!-- Breadcrumb Navigation --> <nav class="mb-8" aria-label="Breadcrumb"> <ol class="flex items-center space-x-2 text-sm text-gray-500"> <li><a href="/" class="hover:text-accent-600">Home</a></li> <li><span class="mx-2">/</span></li> <li><a href="/blog" class="hover:text-accent-600">Blog</a></li> <li><span class="mx-2">/</span></li> <li class="text-gray-900 font-medium">${r}</li> </ol> </nav> <!-- Header --> <section class="text-center mb-12"> <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4"> ${r} </h1> <p class="text-xl text-gray-600 max-w-2xl mx-auto"> ${s.length} post${1===s.length?"":"s"} in this category
</p> </section> <!-- Posts Grid --> ${s.length>0?renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> ${s.map((t=>renderTemplate`${renderComponent(e,"BlogCard",$$BlogCard,{post:t,variant:"default"})}`))} </div>`:renderTemplate`<section class="text-center py-16"> <div class="max-w-md mx-auto"> <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path> </svg> <h3 class="text-xl font-semibold text-gray-900 mb-2">No posts found</h3> <p class="text-gray-600 mb-4">There are no posts in this category yet.</p> <a href="/blog" class="inline-flex items-center px-4 py-2 bg-accent-600 text-white rounded-lg hover:bg-accent-700 transition-colors">
Browse all posts
</a> </div> </section>`} <!-- Back to Blog --> <div class="mt-12 text-center"> <a href="/blog" class="inline-flex items-center text-accent-600 hover:text-accent-700 font-medium"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path> </svg>
Back to all posts
</a> </div> </div> `})}`}),"D:/code/image/polar-image-store/src/pages/blog/category/[category].astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/blog/category/[category].astro",$$url="/blog/category/[category]",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$category,file:$$file,getStaticPaths:getStaticPaths,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};