globalThis.process??={},globalThis.process.env??={};import{g as getCollection}from"./_astro_content_3f_fRm09.mjs";function generateExcerpt(t,e=160){const a=t.replace(/```[\s\S]*?```/g,"").replace(/`[^`]*`/g,"").replace(/!\[.*?\]\(.*?\)/g,"").replace(/\[([^\]]*)\]\([^)]*\)/g,"$1").replace(/[#*_~`]/g,"").replace(/\s+/g," ").trim();if(a.length<=e)return a;const s=a.substring(0,e),o=s.lastIndexOf(" ");return o>0?s.substring(0,o)+"...":s+"..."}async function getPublishedPosts(){return(await getCollection("blog")).filter((t=>!t.data.draft)).sort(((t,e)=>new Date(e.data.publishDate).getTime()-new Date(t.data.publishDate).getTime()))}async function getFeaturedPosts(t=3){return(await getPublishedPosts()).filter((t=>t.data.featured)).slice(0,t)}async function getRecentPosts(t=6){return(await getPublishedPosts()).filter((t=>!t.data.featured)).slice(0,t)}async function getBlogCategories(){const t=await getPublishedPosts(),e=new Map;return t.forEach((t=>{if(t.data.category){const a=e.get(t.data.category)||0;e.set(t.data.category,a+1)}})),Array.from(e.entries()).map((([t,e])=>({name:t,count:e,slug:t.toLowerCase().replace(/\s+/g,"-")}))).sort(((t,e)=>e.count-t.count))}async function getBlogTags(){const t=await getPublishedPosts(),e=new Map;return t.forEach((t=>{t.data.tags&&t.data.tags.forEach((t=>{const a=e.get(t)||0;e.set(t,a+1)}))})),Array.from(e.entries()).map((([t,e])=>({name:t,count:e,slug:t.toLowerCase().replace(/\s+/g,"-")}))).sort(((t,e)=>e.count-t.count))}async function searchPosts(t,e=10){if(!t.trim())return[];const a=await getPublishedPosts(),s=t.toLowerCase();return a.filter((t=>{const e=t.data.title.toLowerCase(),a=t.data.description.toLowerCase(),o=t.body.toLowerCase(),r=t.data.category?.toLowerCase()||"",n=t.data.tags?.join(" ").toLowerCase()||"";return e.includes(s)||a.includes(s)||o.includes(s)||r.includes(s)||n.includes(s)})).slice(0,e)}export{getBlogTags as a,generateExcerpt as b,getFeaturedPosts as c,getRecentPosts as d,getBlogCategories as g,searchPosts as s};