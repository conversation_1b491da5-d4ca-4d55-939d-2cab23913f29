globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,d as renderComponent,r as renderTemplate,m as maybeRenderHead,b as addAttribute}from"../../chunks/astro/server_B4z0x52z.mjs";import{$ as $$Layout}from"../../chunks/Layout_Dwp3-WNN.mjs";import{g as getCollection}from"../../chunks/_astro_content_3f_fRm09.mjs";import{$ as $$StructuredData}from"../../chunks/StructuredData_VefXz0ov.mjs";export{renderers}from"../../renderers.mjs";const $$Astro=createAstro("https://infpik.store");async function getStaticPaths(){return(await getCollection("blog")).filter((a=>!a.data.draft)).map((a=>({params:{slug:a.slug},props:a})))}const $$slug=createComponent((async(a,t,e)=>{const r=a.createAstro($$Astro,t,e);r.self=$$slug;const s=r.props,{Content:d}=await s.render(),o=(await getCollection("blog")).filter((a=>a.slug!==s.slug&&!a.data.draft&&(a.data.category===s.data.category||a.data.tags?.some((a=>s.data.tags?.includes(a)))))).slice(0,3),i=s.body.split(/\s+/).length,n=Math.ceil(i/200),l={headline:s.data.title,description:s.data.description,image:s.data.heroImage?[s.data.heroImage]:[],datePublished:s.data.publishDate.toISOString(),dateModified:s.data.updatedDate?.toISOString()||s.data.publishDate.toISOString(),author:{"@type":"Person",name:s.data.author||"InfPik Team"},publisher:{"@type":"Organization",name:"InfPik",logo:{"@type":"ImageObject",url:"http://infpik.store/logo.svg"}},mainEntityOfPage:{"@type":"WebPage","@id":`http://infpik.store/blog/${s.slug}`}},c={itemListElement:[{"@type":"ListItem",position:1,name:"Home",item:"http://infpik.store"},{"@type":"ListItem",position:2,name:"Blog",item:"http://infpik.store/blog"},{"@type":"ListItem",position:3,name:s.data.title,item:`http://infpik.store/blog/${s.slug}`}]};return renderTemplate`${renderComponent(a,"Layout",$$Layout,{title:`${s.data.title} - InfPik Blog`,description:s.data.description,image:s.data.heroImage,canonical:`http://infpik.store/blog/${s.slug}`,type:"article","data-astro-cid-4sn4zg3r":!0},{default:async a=>renderTemplate`  ${renderComponent(a,"StructuredData",$$StructuredData,{type:"Article",data:l,"data-astro-cid-4sn4zg3r":!0})}  ${renderComponent(a,"StructuredData",$$StructuredData,{type:"BreadcrumbList",data:c,"data-astro-cid-4sn4zg3r":!0})} ${maybeRenderHead()}<article class="w-full px-4 md:px-8 py-8" data-astro-cid-4sn4zg3r> <!-- Breadcrumb Navigation --> <nav class="mb-8" aria-label="Breadcrumb" data-astro-cid-4sn4zg3r> <ol class="flex items-center space-x-2 text-sm text-gray-500" data-astro-cid-4sn4zg3r> <li data-astro-cid-4sn4zg3r><a href="/" class="hover:text-accent-600" data-astro-cid-4sn4zg3r>Home</a></li> <li data-astro-cid-4sn4zg3r><span class="mx-2" data-astro-cid-4sn4zg3r>/</span></li> <li data-astro-cid-4sn4zg3r><a href="/blog" class="hover:text-accent-600" data-astro-cid-4sn4zg3r>Blog</a></li> <li data-astro-cid-4sn4zg3r><span class="mx-2" data-astro-cid-4sn4zg3r>/</span></li> <li class="text-gray-900 font-medium" data-astro-cid-4sn4zg3r>${s.data.title}</li> </ol> </nav> <!-- Article Header --> <header class="mb-8" data-astro-cid-4sn4zg3r> ${s.data.category&&renderTemplate`<div class="mb-4" data-astro-cid-4sn4zg3r> <span class="inline-block px-3 py-1 text-sm font-medium text-accent-700 bg-accent-100 rounded-full" data-astro-cid-4sn4zg3r> ${s.data.category} </span> </div>`} <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4" data-astro-cid-4sn4zg3r> ${s.data.title} </h1> <p class="text-xl text-gray-600 mb-6" data-astro-cid-4sn4zg3r> ${s.data.description} </p> <div class="flex items-center gap-4 text-sm text-gray-500" data-astro-cid-4sn4zg3r> <div class="flex items-center gap-2" data-astro-cid-4sn4zg3r> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-4sn4zg3r> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" data-astro-cid-4sn4zg3r></path> </svg> <time${addAttribute(s.data.publishDate.toISOString(),"datetime")} data-astro-cid-4sn4zg3r> ${s.data.publishDate.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})} </time> </div> <div class="flex items-center gap-2" data-astro-cid-4sn4zg3r> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-4sn4zg3r> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" data-astro-cid-4sn4zg3r></path> </svg> <span data-astro-cid-4sn4zg3r>${n} min read</span> </div> ${s.data.author&&renderTemplate`<div class="flex items-center gap-2" data-astro-cid-4sn4zg3r> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-4sn4zg3r> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" data-astro-cid-4sn4zg3r></path> </svg> <span data-astro-cid-4sn4zg3r>By ${s.data.author}</span> </div>`} </div> </header> <!-- Hero Image --> ${s.data.heroImage&&renderTemplate`<div class="mb-8" data-astro-cid-4sn4zg3r> <img${addAttribute(s.data.heroImage,"src")}${addAttribute(s.data.title,"alt")} class="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg" loading="eager" data-astro-cid-4sn4zg3r> </div>`} <!-- Article Content --> <div class="prose prose-lg max-w-none mb-12" data-astro-cid-4sn4zg3r> ${renderComponent(a,"Content",d,{"data-astro-cid-4sn4zg3r":!0})} </div> <!-- Tags --> ${s.data.tags&&s.data.tags.length>0&&renderTemplate`<div class="mb-12" data-astro-cid-4sn4zg3r> <h3 class="text-lg font-semibold text-gray-900 mb-4" data-astro-cid-4sn4zg3r>Tags</h3> <div class="flex flex-wrap gap-2" data-astro-cid-4sn4zg3r> ${s.data.tags.map((a=>renderTemplate`<a${addAttribute(`/blog/tag/${a}`,"href")} class="inline-block px-3 py-1 text-sm text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors" data-astro-cid-4sn4zg3r>
#${a} </a>`))} </div> </div>`} <!-- Related Posts --> ${o.length>0&&renderTemplate`<section class="border-t border-gray-200 pt-12" data-astro-cid-4sn4zg3r> <h2 class="text-2xl font-bold text-gray-900 mb-8" data-astro-cid-4sn4zg3r>Related Posts</h2> <div class="grid grid-cols-1 md:grid-cols-3 gap-6" data-astro-cid-4sn4zg3r> ${o.map((a=>renderTemplate`<article class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow" data-astro-cid-4sn4zg3r> ${a.data.heroImage&&renderTemplate`<img${addAttribute(a.data.heroImage,"src")}${addAttribute(a.data.title,"alt")} class="w-full h-40 object-cover" loading="lazy" data-astro-cid-4sn4zg3r>`} <div class="p-4" data-astro-cid-4sn4zg3r> <h3 class="text-lg font-semibold text-gray-900 mb-2" data-astro-cid-4sn4zg3r> <a${addAttribute(`/blog/${a.slug}`,"href")} class="hover:text-accent-600 transition-colors" data-astro-cid-4sn4zg3r> ${a.data.title} </a> </h3> <p class="text-gray-600 text-sm line-clamp-2" data-astro-cid-4sn4zg3r> ${a.data.description} </p> </div> </article>`))} </div> </section>`} </article> `})} `}),"D:/code/image/polar-image-store/src/pages/blog/[slug].astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/blog/[slug].astro",$$url="/blog/[slug]",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$slug,file:$$file,getStaticPaths:getStaticPaths,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};